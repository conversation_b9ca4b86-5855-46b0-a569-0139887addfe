import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { StyleSheet, View, TextInput, TouchableOpacity } from "react-native";
import BottomSheet, {
  BottomSheetFlatList,
  BottomSheetSectionList,
  BottomSheetView,
} from "@gorhom/bottom-sheet";
import { Box, Text } from "../restyle";
import { Spacer } from "../spacer";
import { Backdrop } from "../backdrop";
import { palette, spacing } from "@/theme/theme";
import { hp, wp } from "@/utils/responsive";
import { AntDesign, Ionicons } from "@expo/vector-icons";
import { SvgIcon } from "../svg-icon";
import { icons } from "@/assets/icons";
import { ALL_DRAWINGS } from "@gocharting/technical-indicators/lib/constants/AllInteractives";
import fuzzy from "@gocharting/shared-components/src/fuzzy";
import { ScrollView } from "react-native-gesture-handler";
import { AppActions } from "@/contexts";
import { getDrawingIcon } from "../../../lib/utils/drawTypeIcon";

const ALL_NEEDED_DRAWINGS = ALL_DRAWINGS.filter((d) => d.name !== "Tweet");

interface AddIntervalModalProps {
  addIntervalModalRef: React.RefObject<BottomSheet>;
  onAddInterval: () => void;
}

function generateDrawingsMenu(favourite) {
  const order = {
    "Favorites": 1,
    "Lines & Measures": 2,
    Patterns: 3,
    "Sacred Geometry": 4,
    "Shapes & Text": 5,
    Emojis: 6,
    Icons: 7,
    Stickers: 8,
  };

  // Step 1: Group drawings by groupName
  const grouped = ALL_NEEDED_DRAWINGS.reduce((acc, drawing) => {
    const group = drawing.groupName;
    if (!acc[group]) acc[group] = [];
    acc[group].push(drawing);
    return acc;
  }, {});

  // Add favorites section directly from favourite.DRAWINGS
  if (favourite?.DRAWINGS?.length > 0) {
    grouped["Favorites"] = favourite.DRAWINGS.reverse();
  }

  // Step 2: Convert to array with key-value pairs
  const groupedArray = Object.keys(grouped).map((key) => ({
    title: key,
    data: grouped[key],
  }));

  // Step 3: Sort based on custom order
  groupedArray.sort((a, b) => {
    const orderA = order[a.title] || Infinity;
    const orderB = order[b.title] || Infinity;
    return orderA - orderB;
  });

  return groupedArray;
}

const DrawingModal: React.FC<AddIntervalModalProps> = ({
  drawingModalRef,
  favourite,
  format,
}) => {
  const context = useContext(AppActions);
  const snapPoints = useMemo(() => [hp(80)], []);
  const [search, setSearch] = useState<string>("");
  const [list, setList] = useState([]);

  const content = useMemo(() => generateDrawingsMenu(favourite), [favourite]);

  const addToFavorite = (favouriteItem) => {
    const { type, item } = favouriteItem;
    if (item == null) {
      return;
    }
    const favArray = favourite["DRAWINGS"];
    const isPresent = favArray.filter((d) => d.id === item.id).length > 0;
    const newFavArray = isPresent
      ? favArray.filter((d) => d.id !== item.id)
      : [...favArray, item];
    const newFavourite = {
      ...favourite,
      ["DRAWINGS"]: newFavArray,
    };
    context.changeAppState("favourite", newFavourite);
  };

  // useEffect(() => {
  //   let list = [];
  //   const favArray = favourite?.DRAWINGS;
  //   if (format === "favourites") {
  //     const base = ALL_DRAWINGS.filter((elem) => {
  //       return favArray.map((d) => d.id).indexOf(elem.name) > -1;
  //     });
  //     list =
  //       search != null && search.length > 0
  //         ? fuzzy
  //             .filter(search, base, {
  //               pre: "",
  //               post: "",
  //               extract: function (entry) {
  //                 return entry.name;
  //               },
  //             })
  //             .map((each) => each.original)
  //         : base;
  //   } else {
  //     list =
  //       search != null && search.length > 0
  //         ? fuzzy
  //             .filter(search, ALL_DRAWINGS, {
  //               pre: "",
  //               post: "",
  //               extract: function (entry) {
  //                 return entry.name;
  //               },
  //             })
  //             .map((each) => each.original)
  //         : ALL_DRAWINGS;
  //   }

  //   setList(list);
  // }, []);

  const addDrawing = (drawingObject) => {
    context.reduxActions.addDrawingObject(drawingObject);
    drawingModalRef.current?.close();
  };

  const renderItem = useCallback(
    ({ item }) => (
      <TouchableOpacity
        onPress={() => {
          addDrawing(item);
        }}
        onLongPress={() => {
          const favoriteItem = {
            type: "DRAWINGS",
            item: { ...item, id: item.name },
          };
          addToFavorite(favoriteItem);
        }}
        style={styles.itemContainer}
        activeOpacity={0.7}
      >
        <Box
          height={hp(8)}
          width={hp(8)}
          borderRadius={"lg"}
          borderWidth={1}
          borderColor="splitterLine"
          justifyContent={"center"}
          alignItems={"center"}
        >
          {getDrawingIcon(item.name, 24, 24)}
        </Box>
        <Text
          textAlign={"center"}
          variant={"headline"}
          numberOfLines={1}
          style={styles.itemText}
        >
          {item.name.length > 12
            ? `${item.name.substring(0, 12)}...`
            : item.name}
        </Text>
      </TouchableOpacity>
    ),
    [addDrawing, addToFavorite]
  );



  return (
    <BottomSheet
      ref={drawingModalRef}
      index={-1}
      snapPoints={snapPoints}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: palette.white }}
      backdropComponent={Backdrop}
    >
      <Box pb="m" px={"m"}>
        <Box py="m">
          <Text variant="header" textAlign="center">
            Drawings
          </Text>
        </Box>
        <Spacer size={10} />
        <BottomSheetView style={styles.contentContainer}>
          <Box
            flexDirection="row"
            gap="sm"
            alignItems="center"
            justifyContent="space-between"
            my="s"
          >
            <Box
              backgroundColor="TableTileHeader"
              alignItems="center"
              justifyContent="space-between"
              borderRadius="lg"
              pl="mx"
              pr="s"
              py="s"
              flex={1}
              flexDirection="row"
            >
              <Box flex={1} flexDirection="row" gap="s">
                <SvgIcon icon={icons.ic_search} size={20} />
                <TextInput
                  placeholder="Search Drawings..."
                  style={styles.search}
                  value={search}
                  onChangeText={(value) => console.log(value)}
                />
              </Box>
              <Box
                width={30}
                height={30}
                alignItems="center"
                justifyContent="center"
              >
                {/* {searchLoading && (
                                <ActivityIndicator
                                    size='small'
                                    color={palette.primary_purple}
                                />
                            )} */}
              </Box>
            </Box>
          </Box>
        </BottomSheetView>
      </Box>
      <ScrollView>
        {content.map((item) => (
          <Box px={"m"} mb={"m"}>
            <Text my={"m"} variant={"caption"}>
              {item.title}
            </Text>
            <BottomSheetFlatList
              data={item.data}
              horizontal
              renderItem={renderItem}
              keyExtractor={(interval) => interval.name || interval.type?.toString()}
              contentContainerStyle={[styles.listContainer, { gap: 10 }]}
              showsHorizontalScrollIndicator={false}
              ItemSeparatorComponent={() => <View style={{ width: wp(5) }} />}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              windowSize={10}
              initialNumToRender={5}
              getItemLayout={(_, index) => ({
                length: hp(8) + wp(5),
                offset: (hp(8) + wp(5)) * index,
                index,
              })}
            />
          </Box>
        ))}
      </ScrollView>
    </BottomSheet>
  );
};

export default DrawingModal;

const styles = StyleSheet.create({
  contentContainer: {
    alignItems: "center",
  },
  search: {
    fontSize: 16,
    lineHeight: 20,
    color: palette.headline1,
    textTransform: "uppercase",
    flex: 1,
  },
  listContainer: {
    backgroundColor: palette.white,
  },
  flatListHorizontal: {
    flexGrow: 0,
    paddingHorizontal: spacing.mx,
    marginVertical: spacing.mx,
  },
  itemContainer: {
    flexDirection: "column",
    alignItems: "center",
    rowGap: 10,
  },
  itemText: {
    maxWidth: hp(10),
  },
});
