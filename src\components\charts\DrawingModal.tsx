import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView
} from "react-native";
import { FlashList } from "@shopify/flash-list";
import { Box, Text } from "../restyle";
import { Spacer } from "../spacer";
import { palette } from "@/theme/theme";
import { hp, wp } from "@/utils/responsive";
import { AntDesign } from "@expo/vector-icons";
import { SvgIcon } from "../svg-icon";
import { icons } from "@/assets/icons";
import { ALL_DRAWINGS } from "@gocharting/technical-indicators/lib/constants/AllInteractives";
import { AppActions } from "@/contexts";
import { getDrawingIcon } from "../../../lib/utils/drawTypeIcon";
const ALL_NEEDED_DRAWINGS = ALL_DRAWINGS.filter((d) => d.name !== "Tweet");

interface DrawingModalProps {
  visible: boolean;
  onClose: () => void;
  favourite: any;
  format?: string;
}

function generateDrawingsMenu(favourite: any) {
  const order: { [key: string]: number } = {
    "Favorites": 1,
    "Lines & Measures": 2,
    Patterns: 3,
    "Sacred Geometry": 4,
    "Shapes & Text": 5,
    Emojis: 6,
    Icons: 7,
    Stickers: 8,
  };

  // Step 1: Group drawings by groupName
  const grouped = ALL_NEEDED_DRAWINGS.reduce((acc: any, drawing: any) => {
    const group = drawing.groupName;
    if (!acc[group]) acc[group] = [];
    acc[group].push(drawing);
    return acc;
  }, {});

  // Add favorites section directly from favourite.DRAWINGS
  if (favourite?.DRAWINGS?.length > 0) {
    grouped["Favorites"] = [...favourite.DRAWINGS].reverse();
  }

  // Step 2: Convert to array with key-value pairs
  const groupedArray = Object.keys(grouped).map((key) => ({
    title: key,
    data: grouped[key],
  }));

  // Step 3: Sort based on custom order
  groupedArray.sort((a, b) => {
    const orderA = order[a.title] || Infinity;
    const orderB = order[b.title] || Infinity;
    return orderA - orderB;
  });

  return groupedArray;
}

const DrawingModal: React.FC<DrawingModalProps> = ({
  visible,
  onClose,
  favourite,
  format,
}) => {
  const context = useContext(AppActions);
  const [search, setSearch] = useState<string>("");

  const content = useMemo(() => generateDrawingsMenu(favourite), [favourite]);

  const addToFavorite = (favouriteItem: any) => {
    const { type, item } = favouriteItem;
    if (item == null) {
      return;
    }
    const favArray = favourite["DRAWINGS"];
    const isPresent = favArray.filter((d: any) => d.id === item.id).length > 0;
    const newFavArray = isPresent
      ? favArray.filter((d: any) => d.id !== item.id)
      : [...favArray, item];
    const newFavourite = {
      ...favourite,
      ["DRAWINGS"]: newFavArray,
    };
    (context as any).changeAppState("favourite", newFavourite);
  };

  // useEffect(() => {
  //   let list = [];
  //   const favArray = favourite?.DRAWINGS;
  //   if (format === "favourites") {
  //     const base = ALL_DRAWINGS.filter((elem) => {
  //       return favArray.map((d) => d.id).indexOf(elem.name) > -1;
  //     });
  //     list =
  //       search != null && search.length > 0
  //         ? fuzzy
  //             .filter(search, base, {
  //               pre: "",
  //               post: "",
  //               extract: function (entry) {
  //                 return entry.name;
  //               },
  //             })
  //             .map((each) => each.original)
  //         : base;
  //   } else {
  //     list =
  //       search != null && search.length > 0
  //         ? fuzzy
  //             .filter(search, ALL_DRAWINGS, {
  //               pre: "",
  //               post: "",
  //               extract: function (entry) {
  //                 return entry.name;
  //               },
  //             })
  //             .map((each) => each.original)
  //         : ALL_DRAWINGS;
  //   }

  //   setList(list);
  // }, []);

  const addDrawing = (drawingObject: any) => {
    (context as any).reduxActions.addDrawingObject(drawingObject);
    onClose();
  };

  const renderItem = useCallback(
    ({ item }: { item: any }) => {
      const handlePress = () => addDrawing(item);
      const handleLongPress = () => {
        const favoriteItem = {
          type: "DRAWINGS",
          item: { ...item, id: item.name },
        };
        addToFavorite(favoriteItem);
      };

      const displayName = item.name.length > 12
        ? `${item.name.substring(0, 12)}...`
        : item.name;

      return (
        <TouchableOpacity
          onPress={handlePress}
          onLongPress={handleLongPress}
          style={styles.itemContainer}
          activeOpacity={0.9}
        >
          <Box style={styles.iconContainer}>
            {getDrawingIcon(item.name, 24, 24)}
          </Box>
          <Text
            textAlign={"center"}
            variant={"headline"}
            numberOfLines={1}
            style={styles.itemText}
          >
            {displayName}
          </Text>
        </TouchableOpacity>
      );
    },
    [addDrawing, addToFavorite]
  );
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <Box flex={1} backgroundColor="backgroundColor">
          <Box pb="m" px={"m"} pt="m">
            <Box py="m" flexDirection="row" alignItems="center" justifyContent="space-between">
              <TouchableOpacity onPress={onClose}>
                <AntDesign name="close" size={24} color={palette.headline1} />
              </TouchableOpacity>
              <Text variant="header" textAlign="center">
                Drawings
              </Text>
              <View style={styles.spacer} />
            </Box>
            <Spacer size={10} />
            <View style={styles.contentContainer}>
              <Box
                flexDirection="row"
                gap="sm"
                alignItems="center"
                justifyContent="space-between"
                my="s"
              >
                <Box
                  backgroundColor="TableTileHeader"
                  alignItems="center"
                  justifyContent="space-between"
                  borderRadius="lg"
                  pl="mx"
                  pr="s"
                  py="s"
                  flex={1}
                  flexDirection="row"
                >
                  <Box flex={1} flexDirection="row" gap="s">
                    <SvgIcon icon={icons.ic_search} size={20} />
                    <TextInput
                      placeholder="Search Drawings..."
                      style={styles.search}
                      value={search}
                      onChangeText={(value) => console.log(value)}
                    />
                  </Box>
                  <Box
                    width={30}
                    height={30}
                    alignItems="center"
                    justifyContent="center"
                  >
                    {/* {searchLoading && (
                                    <ActivityIndicator
                                        size='small'
                                        color={palette.primary_purple}
                                    />
                                )} */}
                  </Box>
                </Box>
              </Box>
            </View>
          </Box>
          <ScrollView>
            {content.map((item, index) => (
              <Box key={index} px={"m"} mb={"m"}>
                <Text my={"m"} variant={"caption"}>
                  {item.title}
                </Text>
                <FlashList
                  data={item.data}
                  horizontal
                  renderItem={renderItem}
                  keyExtractor={(interval: any, itemIndex: number) =>
                    `${index}-${itemIndex}-${interval.name || interval.type}`
                  }
                  estimatedItemSize={hp(8) + wp(5)}
                  showsHorizontalScrollIndicator={false}
                  ItemSeparatorComponent={() => <View style={{ width: wp(5) }} />}
                  removeClippedSubviews={true}
                  scrollEventThrottle={1}
                  decelerationRate="fast"
                  bounces={false}
                  overScrollMode="never"
                  getItemType={() => "drawing-item"}
                  disableAutoLayout={true}
                  drawDistance={200}
                  contentContainerStyle={{ paddingHorizontal: 0 }}
                />
              </Box>
            ))}
          </ScrollView>
        </Box>
      </SafeAreaView>
    </Modal>
  );
};

export default DrawingModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: palette.background,
  },
  contentContainer: {
    alignItems: "center",
  },
  search: {
    fontSize: 16,
    lineHeight: 20,
    color: palette.headline1,
    textTransform: "uppercase",
    flex: 1,
  },
  listContainer: {
    backgroundColor: palette.white,
  },
  itemContainer: {
    flexDirection: "column",
    alignItems: "center",
    rowGap: 10,
    width: hp(8) + wp(5),
  },
  iconContainer: {
    height: hp(8),
    width: hp(8),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: palette.splitterLine,
    justifyContent: "center",
    alignItems: "center",
  },
  itemText: {
    maxWidth: hp(10),
  },
  spacer: {
    width: 24,
  },
});
