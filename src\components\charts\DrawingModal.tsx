import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  Modal,
  FlatList,
  ScrollView
} from "react-native";
import { Box, Text } from "../restyle";
import { Spacer } from "../spacer";
import { palette, spacing } from "@/theme/theme";
import { hp, wp } from "@/utils/responsive";
import { AntDesign } from "@expo/vector-icons";
import { SvgIcon } from "../svg-icon";
import { icons } from "@/assets/icons";
import { ALL_DRAWINGS } from "@gocharting/technical-indicators/lib/constants/AllInteractives";

import { AppActions } from "@/contexts";
import { getDrawingIcon } from "../../../lib/utils/drawTypeIcon";

const ALL_NEEDED_DRAWINGS = ALL_DRAWINGS.filter((d) => d.name !== "Tweet");

// Global counter to ensure unique keys
let globalKeyCounter = 0;

interface DrawingModalProps {
  visible: boolean;
  onClose: () => void;
  favourite: any;
  format?: string;
}

function generateDrawingsMenu(favourite: any) {
  const order: { [key: string]: number } = {
    "Favorites": 1,
    "Lines & Measures": 2,
    Patterns: 3,
    "Sacred Geometry": 4,
    "Shapes & Text": 5,
    Emojis: 6,
    Icons: 7,
    Stickers: 8,
  };

  // Get favorite drawing names for filtering
  const favoriteNames = favourite?.DRAWINGS?.map((fav: any) => fav.name) || [];

  // Step 1: Group drawings by groupName, excluding favorites from other categories
  const grouped = ALL_NEEDED_DRAWINGS.reduce((acc: any, drawing: any) => {
    const group = drawing.groupName;
    if (!acc[group]) acc[group] = [];

    // Only add to category if it's not in favorites and not already in this group
    if (!favoriteNames.includes(drawing.name)) {
      // Check if this drawing is already in this group to prevent duplicates
      const existsInGroup = acc[group].some((existingDrawing: any) =>
        existingDrawing.name === drawing.name || existingDrawing.type === drawing.type
      );

      if (!existsInGroup) {
        acc[group].push(drawing);
      }
    }

    return acc;
  }, {});

  // Add favorites section at the beginning if there are any
  if (favourite?.DRAWINGS?.length > 0) {
    // Deduplicate favorites as well
    const uniqueFavorites = [...favourite.DRAWINGS].filter((fav: any, index: number, arr: any[]) =>
      arr.findIndex((item: any) => item.name === fav.name || item.type === fav.type) === index
    );
    grouped["Favorites"] = uniqueFavorites.reverse();
  }

  // Step 2: Convert to array with key-value pairs
  const groupedArray = Object.keys(grouped).map((key) => ({
    title: key,
    data: grouped[key],
  }));

  // Step 3: Sort based on custom order
  groupedArray.sort((a, b) => {
    const orderA = order[a.title] || Infinity;
    const orderB = order[b.title] || Infinity;
    return orderA - orderB;
  });

  return groupedArray;
}

const DrawingModal: React.FC<DrawingModalProps> = ({
  visible,
  onClose,
  favourite,
  format,
}) => {
  const context = useContext(AppActions);
  const [search, setSearch] = useState<string>("");

  const content = useMemo(() => {
    // Reset global counter for fresh keys each time content is generated
    globalKeyCounter = 0;
    return generateDrawingsMenu(favourite);
  }, [favourite]);

  const addToFavorite = (favouriteItem: any) => {
    const { type, item } = favouriteItem;
    if (item == null) {
      return;
    }
    const favArray = favourite["DRAWINGS"];
    const isPresent = favArray.filter((d: any) => d.id === item.id).length > 0;
    const newFavArray = isPresent
      ? favArray.filter((d: any) => d.id !== item.id)
      : [...favArray, item];
    const newFavourite = {
      ...favourite,
      ["DRAWINGS"]: newFavArray,
    };
    (context as any).changeAppState("favourite", newFavourite);
  };

  // useEffect(() => {
  //   let list = [];
  //   const favArray = favourite?.DRAWINGS;
  //   if (format === "favourites") {
  //     const base = ALL_DRAWINGS.filter((elem) => {
  //       return favArray.map((d) => d.id).indexOf(elem.name) > -1;
  //     });
  //     list =
  //       search != null && search.length > 0
  //         ? fuzzy
  //             .filter(search, base, {
  //               pre: "",
  //               post: "",
  //               extract: function (entry) {
  //                 return entry.name;
  //               },
  //             })
  //             .map((each) => each.original)
  //         : base;
  //   } else {
  //     list =
  //       search != null && search.length > 0
  //         ? fuzzy
  //             .filter(search, ALL_DRAWINGS, {
  //               pre: "",
  //               post: "",
  //               extract: function (entry) {
  //                 return entry.name;
  //               },
  //             })
  //             .map((each) => each.original)
  //         : ALL_DRAWINGS;
  //   }

  //   setList(list);
  // }, []);

  const addDrawing = (drawingObject: any) => {
    (context as any).reduxActions.addDrawingObject(drawingObject);
    onClose();
  };

  const renderItem = useCallback(
    ({ item }: { item: any }) => (
      <TouchableOpacity
        onPress={() => {
          addDrawing(item);
        }}
        onLongPress={() => {
          const favoriteItem = {
            type: "DRAWINGS",
            item: { ...item, id: item.name },
          };
          addToFavorite(favoriteItem);
        }}
        style={styles.itemContainer}
        activeOpacity={0.7}
      >
        <Box
          height={hp(8)}
          width={hp(8)}
          borderRadius={"lg"}
          borderWidth={1}
          borderColor="splitterLine"
          justifyContent={"center"}
          alignItems={"center"}
        >
          {getDrawingIcon(item.name, 24, 24)}
        </Box>
        <Text
          textAlign={"center"}
          variant={"headline"}
          numberOfLines={1}
          style={styles.itemText}
        >
          {item.name.length > 12
            ? `${item.name.substring(0, 12)}...`
            : item.name}
        </Text>
      </TouchableOpacity>
    ),
    [addDrawing, addToFavorite]
  );



  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <Box flex={1} backgroundColor="backgroundColor">
        <Box pb="m" px={"m"} pt="xl">
          <Box py="m" flexDirection="row" alignItems="center" justifyContent="space-between">
            <TouchableOpacity onPress={onClose}>
              <AntDesign name="close" size={24} color={palette.headline1} />
            </TouchableOpacity>
            <Text variant="header" textAlign="center">
              Drawings
            </Text>
            <View style={styles.spacer} />
          </Box>
          <Spacer size={10} />
          <View style={styles.contentContainer}>
            <Box
              flexDirection="row"
              gap="sm"
              alignItems="center"
              justifyContent="space-between"
              my="s"
            >
              <Box
                backgroundColor="TableTileHeader"
                alignItems="center"
                justifyContent="space-between"
                borderRadius="lg"
                pl="mx"
                pr="s"
                py="s"
                flex={1}
                flexDirection="row"
              >
                <Box flex={1} flexDirection="row" gap="s">
                  <SvgIcon icon={icons.ic_search} size={20} />
                  <TextInput
                    placeholder="Search Drawings..."
                    style={styles.search}
                    value={search}
                    onChangeText={(value) => console.log(value)}
                  />
                </Box>
                <Box
                  width={30}
                  height={30}
                  alignItems="center"
                  justifyContent="center"
                >
                  {/* {searchLoading && (
                                  <ActivityIndicator
                                      size='small'
                                      color={palette.primary_purple}
                                  />
                              )} */}
                </Box>
              </Box>
            </Box>
          </View>
        </Box>
        <ScrollView>
          {content.map((item, categoryIndex) => (
            <Box key={`category-${categoryIndex}`} px={"m"} mb={"m"}>
              <Text my={"m"} variant={"caption"}>
                {item.title}
              </Text>
              <FlatList
                data={item.data}
                horizontal
                renderItem={renderItem}
                keyExtractor={(interval: any, itemIndex: number) => {
                  // Create a unique key using multiple identifiers and global counter
                  const categoryTitle = item.title.replace(/\s+/g, '_');
                  const itemName = interval.name || interval.type || 'unknown';
                  const itemType = interval.type || 'default';
                  const uniqueId = interval.id || interval.name || interval.type || itemIndex;
                  const globalId = ++globalKeyCounter; // Increment and use global counter

                  return `${categoryTitle}_${itemName}_${itemType}_${uniqueId}_${itemIndex}_${globalId}`;
                }}
                contentContainerStyle={[styles.listContainer, { gap: 10 }]}
                showsHorizontalScrollIndicator={false}
                ItemSeparatorComponent={() => <View style={{ width: wp(5) }} />}
                removeClippedSubviews={true}
                maxToRenderPerBatch={10}
                windowSize={10}
                initialNumToRender={5}
                getItemLayout={(_: any, index: number) => ({
                  length: hp(8) + wp(5),
                  offset: (hp(8) + wp(5)) * index,
                  index,
                })}
                decelerationRate="fast"
                scrollEventThrottle={16}
              />
            </Box>
          ))}
        </ScrollView>
      </Box>
    </Modal>
  );
};

export default DrawingModal;

const styles = StyleSheet.create({
  contentContainer: {
    alignItems: "center",
  },
  search: {
    fontSize: 16,
    lineHeight: 20,
    color: palette.headline1,
    textTransform: "uppercase",
    flex: 1,
  },
  listContainer: {
    backgroundColor: palette.white,
  },
  flatListHorizontal: {
    flexGrow: 0,
    paddingHorizontal: spacing.mx,
    marginVertical: spacing.mx,
  },
  itemContainer: {
    flexDirection: "column",
    alignItems: "center",
    rowGap: 10,
  },
  itemText: {
    maxWidth: hp(10),
  },
  spacer: {
    width: 24,
  },
});
