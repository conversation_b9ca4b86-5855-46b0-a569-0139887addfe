import React, { useMemo, useCallback } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  Modal,
  SafeAreaView,
  FlatList,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Box, Text } from "@/components/restyle";
import { Spacer } from "../spacer";
import { hp } from "@/utils/responsive";
import { palette, Theme } from "@/theme/theme";
import { head } from "lodash";
import { INTERVAL_LIST } from "@gocharting/technical-indicators/lib/constants/AllIntervals";
import { useTheme } from "@shopify/restyle";

interface IntervalListItem {
  type: string;
  [key: string]: any;
}

interface FavoriteInterval {
  id: string;
  type: string;
}

interface Favourite {
  INTERVALS: FavoriteInterval[];
}

interface Security {
  exchange_info?: {
    valid_intervals?: string[];
  };
}

interface IntervalModalProps {
  visible: boolean;
  onClose: () => void;
  handleAddInterval: () => void;
  favourite: Favourite;
  addToFavorite: (payload: {
    type: string;
    item: { id: string; type: string };
  }) => void;
  selectedInterval: string;
  handleChangeInterval: (interval: string) => void;
  security?: Security;
}

const IntervalModal: React.FC<IntervalModalProps> = ({
  visible,
  onClose,
  handleAddInterval,
  favourite,
  addToFavorite,
  selectedInterval,
  handleChangeInterval,
  security,
}) => {

  const selectedSecurity = security;

  const renderItem = useCallback(
    ({ item }: { item: string }) => {
      const intervals = INTERVAL_LIST(item) as IntervalListItem[];
      const elem = head(intervals.filter((e) => e.type === item)) as
        | IntervalListItem
        | undefined;

      const favArray = favourite?.INTERVALS || [];
      const isPresent =
        !!elem && favArray.filter((fav) => fav.id === elem.type).length > 0;

      const isActive = selectedInterval === item ? "active" : "";

      return (
        <Box px={"m"} style={[styles.intervalItem, { borderBottomColor: colors.input_Border }]}>
          <TouchableOpacity
            onPress={() => handleChangeInterval(item)}
            style={styles.intervalTouch}
          >
            <Text
              variant="content"
              style={[
                styles.intervalText,
                isActive && styles.selectedText,
                {
                  color: isActive ? '#6A0DAD' : colors.headline,
                },
              ]}
            >
              {formatInterval(item)}
            </Text>
          </TouchableOpacity>
          {elem && (
            <TouchableOpacity
              style={styles.starButton}
              onPress={() =>
                addToFavorite({
                  type: "INTERVALS",
                  item: {
                    ...elem,
                    id: elem.type,
                  },
                })
              }
            >
              {isPresent ? (
                <Ionicons name="star" size={20} color="gold" />
              ) : (
                <Ionicons name="star-outline" size={20} color="gray" />
              )}
            </TouchableOpacity>
          )}
        </Box>
      );
    },
    [
      favourite?.INTERVALS,
      handleChangeInterval,
      addToFavorite,
      selectedInterval,
    ]
  );

  const renderFooter = useCallback(
    () => (
      <TouchableOpacity onPress={handleAddInterval} style={styles.addInterval}>
        <Text variant="content" style={styles.addIntervalText}>
          + Add Custom Interval
        </Text>
      </TouchableOpacity>
    ),
    [handleAddInterval]
  );

  const { colors } = useTheme<Theme>();
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <Box flex={1} backgroundColor="backgroundColor">
          <Box
            py="m"
            px="m"
            flexDirection="row"
            justifyContent="space-between"
            alignItems="center"
            style={styles.headerBox}
          >
            <Box width={24} />
            <Text variant="header" textAlign="center" color="headline">
              Interval
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={palette.text} />
            </TouchableOpacity>
          </Box>
          <Spacer size={10} />

          <FlatList
            data={selectedSecurity?.exchange_info?.valid_intervals || []}
            renderItem={renderItem}
            keyExtractor={(interval: string) => interval}
            contentContainerStyle={[styles.listContainer, { backgroundColor: colors.backgroundColor }]}
            ListFooterComponent={renderFooter}
            showsVerticalScrollIndicator={true}
          />
        </Box>
      </SafeAreaView>
    </Modal>
  );
};

export default IntervalModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: palette.background,
  },
  intervalItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomWidth: 1,
    // borderBottomColor: "#F0EDF3",
  },
  intervalText: {
    fontSize: 14,
    // color: "#240253",
  },
  selectedText: {
    // color: "#6A0DAD",
    fontWeight: "bold",
  },
  intervalTouch: {
    flex: 0.7,
    paddingVertical: hp(2.2),
  },
  starButton: {
    flex: 0.3,
    alignItems: "flex-end",
    paddingVertical: hp(2.2),
  },
  addInterval: {
    paddingVertical: 15,
    alignItems: "center",
  },
  addIntervalText: {
    color: "#6A0DAD",
  },
  headerBox: {},
  sheetBackground: {
    // backgroundColor: palette.gray,
  },
  listContainer: {
    paddingBottom: 100,
    // backgroundColor: palette.white,
  },
});

function getScaleFromShortForm(scale: string): string {
  switch (scale) {
    case "m":
      return "Minutes";
    case "h":
      return "Hours";
    case "D":
    case "Day":
      return "Day";
    case "W":
    case "Weeks":
      return "Week";
    case "M":
    case "Months":
      return "Month";
    default:
      return scale;
  }
}

function formatInterval(item: string): string {
  const match = item.match(/^([0-9]+)([a-zA-Z]+)$/);
  if (!match) return item;
  const [, number, unit] = match;
  return `${number} ${getScaleFromShortForm(unit)}`;
}
