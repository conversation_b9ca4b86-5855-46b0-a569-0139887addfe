import React, { useCallback, useMemo, useState } from "react";
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
  ScrollView,
  SafeAreaView,
  FlatList,
} from "react-native";
import { Box, Text } from "../restyle";
import { Spacer } from "../spacer";
import { palette, spacing, AlbertSans, Theme } from "@/theme/theme";
import { SvgIcon } from "../svg-icon";
import { icons } from "@/assets/icons";
import Toast from "react-native-toast-message";
import { ToggleSwitch } from "../common/ToggleSwitch";
import { useTradeForm, TradeFormValues } from "@/hooks/useTradeForm";
import PriceTypePopup from "./PriceTypePopup";
import { useTheme } from "@shopify/restyle";

interface TradeModalProps {
  visible: boolean;
  onClose: () => void;
  security?: any;
  onPlaceTrade: (order: any) => void;
}

const TradeModal: React.FC<TradeModalProps> = ({
  visible,
  onClose,
  security,
  onPlaceTrade,
}) => {

  // Form validation function
  const validateForm = (values: TradeFormValues) => {
    const errors: Record<string, string> = {};

    if (!values.size || Number(values.size) <= 0) {
      errors.size = "Please enter a valid size";
    }

    if (
      (values.orderType === "limit" || values.orderType === "stoplimit") &&
      (!values.price || Number(values.price) <= 0)
    ) {
      errors.price = "Please enter a valid price for limit order";
    }

    if (
      (values.orderType === "stop" || values.orderType === "market" || values.orderType === "stoplimit") &&
      (!values.stopPrice || Number(values.stopPrice) <= 0)
    ) {
      errors.stopPrice = "Please enter a valid stop price";
    }

    // Validate stopLimit if stopLimitEnabled is true
    if (
      values.stopLimitEnabled &&
      (!values.stopLimit || Number(values.stopLimit) <= 0)
    ) {
      errors.stopLimit = "Please enter a valid stop limit price";
    }

    // Validate icebergLegs for market and stoplimit orders
    if (
      (values.orderType === "market" || values.orderType === "stoplimit") &&
      (!values.icebergLegs || Number(values.icebergLegs) <= 0)
    ) {
      errors.icebergLegs = "Please enter a valid number for iceberg legs";
    }

    return errors;
  };

  // State for price type popup
  const [showPriceTypePopup, setShowPriceTypePopup] = useState(false);
  const [priceType, setPriceType] = useState("price");
  const [takeProfitPriceType, setTakeProfitPriceType] = useState("price");
  const [stopLossPriceType, setStopLossPriceType] = useState("price");
  const [activeField, setActiveField] = useState<
    "price" | "takeProfit" | "stopLoss" | null
  >(null);

  // Initialize form with useTradeForm hook
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setValues,
    resetForm,
    validateForm: validateFormValues,
  } = useTradeForm(
    {
      side: "buy",
      orderType: "limit",
      price: "",
      stopPrice: "",
      stopLimit: "",
      stopLimitEnabled: false,
      size: "",
      takeProfit: "",
      stopLoss: "",
      icebergLegs: "2",
      takeProfitEnabled: false,
      stopLossEnabled: false,
    },
    validateForm
  );

  // Log form values on changes for debugging
  React.useEffect(() => {
    console.log('Form values updated:', values);
  }, [values]);

  const handlePriceTypePress = (field: "price" | "takeProfit" | "stopLoss") => {
    setActiveField(field);
    setShowPriceTypePopup(true);
  };

  const handleSelectPriceType = (type: string) => {
    if (activeField === "price") {
      setPriceType(type);
    } else if (activeField === "takeProfit") {
      setTakeProfitPriceType(type);
    } else if (activeField === "stopLoss") {
      setStopLossPriceType(type);
    }
    setShowPriceTypePopup(false);
    setActiveField(null);
  };

  const getActivePriceType = () => {
    if (activeField === "price") {
      return priceType;
    } else if (activeField === "takeProfit") {
      return takeProfitPriceType;
    } else if (activeField === "stopLoss") {
      return stopLossPriceType;
    }
    return "price";
  };

  // Destructure form values for easier access
  const {
    side,
    orderType,
    price,
    stopPrice,
    stopLimit,
    stopLimitEnabled,
    size,
    takeProfit,
    stopLoss,
    icebergLegs,
    takeProfitEnabled,
    stopLossEnabled,
  } = values;

  const handlePlaceTrade = useCallback(() => {
    // Check for validation errors
    const validationErrors = validateFormValues();
    if (Object.keys(validationErrors).length > 0) {
      // Show first error as toast
      const firstError = Object.values(validationErrors)[0];
      Toast.show({
        type: "error",
        text1: "Validation Error",
        text2: firstError,
      });
      return;
    }

    // Log all form data
    console.log('Trade form submitted with data:', {
      ...values,
      priceType,
      takeProfitPriceType,
      stopLossPriceType
    });

    // Create order object
    const order = {
      productId: security?.productId,
      price: values.price ? Number(values.price) : "",
      stopPrice: values.stopPrice ? Number(values.stopPrice) : "",
      stopLimit: values.stopLimitEnabled && values.stopLimit ? Number(values.stopLimit) : "",
      takeProfit: values.takeProfitEnabled && values.takeProfit ? Number(values.takeProfit) : "",
      stopLoss: values.stopLossEnabled && values.stopLoss ? Number(values.stopLoss) : "",
      size: Number(values.size),
      task: "placement",
      side: values.side,
      orderType: values.orderType,
      broker: "gocharting", // Default broker
      icebergLegs: Number(values.icebergLegs),
      priceType,
      takeProfitPriceType,
      stopLossPriceType
    };

    // Call the onPlaceTrade callback
    onPlaceTrade(order);

    // Close the modal
    onClose();

    // Reset form
    resetForm();
  }, [
    values,
    security,
    onPlaceTrade,
    onClose,
    resetForm,
    validateFormValues,
    priceType,
    takeProfitPriceType,
    stopLossPriceType
  ]);

  console.log("security?.symbol ", security?.symbol)

  const { colors } = useTheme<Theme>();

  // Memoized tabs array for order types
  const orderTypeTabs = useMemo(() => [
    { type: "market", label: "Market" },
    { type: "limit", label: "Limit" },
    { type: "stop", label: "Stop" },
    { type: "stoplimit", label: "StopLimit" },
  ], []);

  // Key extractor for FlatList
  const keyExtractor = useCallback((item: any) => item.type, []);

  // Optimized tab renderer for FlatList
  const renderTabItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() => handleChange("orderType", item.type)}
      style={[
        styles.orderTypeButton,
        {
          backgroundColor: orderType === item.type ? colors.headline : colors.input_Border,
        },
      ]}
    >
      <Text
        variant="content"
        color={orderType === item.type ? "freeTrialText" : "headline"}
      >
        {item.label}
      </Text>
    </TouchableOpacity>
  ), [orderType, handleChange, colors]);

  return (
    <>
      {showPriceTypePopup && (
        <TouchableWithoutFeedback onPress={() => setShowPriceTypePopup(false)}>
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            zIndex={999}
          />
        </TouchableWithoutFeedback>
      )}
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={onClose}
      >
        <SafeAreaView style={[styles.modalContainer]}>
          <Box flex={1} style={[styles.modalContent, { backgroundColor: colors.backgroundColor }]}>
            <Box pb="xxl" >
              <Box
                py="m"
                px="m"
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
                my='m'
              >
                <Box width={24} />
                <Box>
                  <Text variant="header" textAlign="center" >
                    {security?.symbol || "TSLA"}
                  </Text>
                  <Text variant="caption" textAlign="center" color="text">
                    {side === "buy" ? "Buy" : "Sell"} {size || "3"}{" "}
                    {security?.source_id || "US:SPOT:TSLA:LMT"}
                    {orderType === "limit"
                      ? ` @${price || "249.5"}`
                      : orderType === "stop"
                        ? ` @${stopPrice || "249.5"}`
                        : orderType === "market"
                          ? " (Market)"
                          : orderType === "stoplimit"
                            ? ` @${stopPrice || "249.5"} / ${price || "249.5"}`
                            : ""}
                  </Text>
                </Box>
                <TouchableOpacity onPress={onClose}>
                  <SvgIcon icon={icons.ic_cross} size={24} color={palette.text} />
                </TouchableOpacity>
              </Box>

              {/* Gocharting Header */}
              <Box
                backgroundColor="primary_purple"
                py="s"
                alignItems="center"
                flexDirection="row"
                justifyContent="center"
                mb="m"
              >
                <Box
                  width={10}
                  height={10}
                  style={{ backgroundColor: palette.primary_green, borderRadius: 5 }}
                  marginRight="m"
                />
                <Text variant="content" color="white" textAlign="center">
                  Gocharting
                </Text>
              </Box>

              <ScrollView
                showsVerticalScrollIndicator={true}
                contentContainerStyle={{ paddingBottom: 50 }}
              >
                <Box px="m">
                  {/* Order Type Tabs - Horizontally Scrollable */}
                  <FlatList
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.tabScrollContainer}
                    style={styles.tabScrollView}
                    data={orderTypeTabs}
                    keyExtractor={keyExtractor}
                    renderItem={renderTabItem}
                    removeClippedSubviews={false}
                    initialNumToRender={4}
                    nestedScrollEnabled={true}
                  />

                  {/* Market Order Fields */}
                  {orderType === "market" && (
                    <>
                      {/* Quantity Input */}
                      <Box mb="m">
                        <Box
                          flexDirection="row"
                          justifyContent="space-between"
                          alignItems="center"
                          mb="xs"
                        >
                          <Box flexDirection="row" alignItems="center">
                            <Text
                              variant="contentSmall"
                              color="caption_text_secondary"
                              fontFamily={AlbertSans.SEMI_BOLD}
                            >
                              Quantity
                            </Text>
                            <TouchableOpacity style={{ marginLeft: 4 }}>
                              <SvgIcon icon={icons.ic_refresh} size={15} />
                            </TouchableOpacity>
                          </Box>
                          <Text variant="caption" color="caption_text_secondary">
                            1 Lot
                          </Text>
                        </Box>
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="input_Border"
                          borderRadius="s"
                          backgroundColor="cardBg"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { textAlign: "center", color: colors.headline }]}
                            placeholder="Enter quantity"
                            placeholderTextColor={colors.headline}
                            value={size}
                            onChangeText={(value) => handleChange("size", value)}
                            onBlur={() => handleBlur("size")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(size || "0");
                              if (currentValue > 0) {
                                handleChange("size", (currentValue - 1).toString());
                              }
                            }}
                          >
                            <Text style={{ fontSize: 20, color: colors.headline }}>−</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(size || "0");
                              handleChange("size", (currentValue + 1).toString());
                            }}
                          >
                            <Text style={{ fontSize: 20, color: colors.headline }}>+</Text>
                          </TouchableOpacity>
                        </Box>
                      </Box>

                      {/* Stop Price Input */}
                      <Box mb="m">
                        <Box
                          flexDirection="row"
                          justifyContent="space-between"
                          alignItems="center"
                          mb="xs"
                        >
                          <Box flexDirection="row" alignItems="center">
                            <Text
                              variant="contentSmall"
                              color="caption_text_secondary"
                              fontFamily={AlbertSans.SEMI_BOLD}
                            >
                              Stop Price
                            </Text>
                            <TouchableOpacity style={{ marginLeft: 4 }}>
                              <SvgIcon icon={icons.ic_refresh} size={15} />
                            </TouchableOpacity>
                          </Box>
                        </Box>
                        <Box position="relative">
                          <Box
                            flexDirection="row"
                            borderWidth={1}
                            borderColor="outline"
                            borderRadius="m"
                            backgroundColor="white"
                            overflow="hidden"
                          >
                            <TextInput
                              style={[styles.styledInput, { textAlign: 'center', color: colors.headline }]}
                              placeholder="Enter stop price"
                              value={stopPrice}
                              onChangeText={(value) =>
                                handleChange("stopPrice", value)
                              }
                              onBlur={() => handleBlur("stopPrice")}
                              keyboardType="numeric"
                            />
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(stopPrice || "0");
                                if (currentValue > 0) {
                                  const newValue = (currentValue - (security?.tick_size || 0.1)).toFixed(1);
                                  handleChange("stopPrice", newValue);
                                }
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(stopPrice || "0");
                                const newValue = (currentValue + (security?.tick_size || 0.1)).toFixed(1);
                                handleChange("stopPrice", newValue);
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                            </TouchableOpacity>
                          </Box>
                        </Box>
                      </Box>

                      {/* Iceberg Legs Input */}
                      <Box mb="m">
                        <Text
                          variant="contentSmall"
                          color="caption_text_secondary"
                          fontFamily={AlbertSans.SEMI_BOLD}
                          mb="xs"
                        >
                          Iceberg Legs
                        </Text>
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="outline"
                          borderRadius="m"
                          backgroundColor="white"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { textAlign: "center", color: colors.headline }]}
                            placeholder="Enter iceberg legs"
                            value={icebergLegs}
                            onChangeText={(value) => handleChange("icebergLegs", value)}
                            onBlur={() => handleBlur("icebergLegs")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(icebergLegs || "0");
                              if (currentValue > 0) {
                                handleChange("icebergLegs", (currentValue - 1).toString());
                              }
                            }}
                          >
                            <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(icebergLegs || "0");
                              handleChange("icebergLegs", (currentValue + 1).toString());
                            }}
                          >
                            <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                          </TouchableOpacity>
                        </Box>
                      </Box>
                    </>
                  )}

                  {/* StopLimit Order Fields */}
                  {orderType === "stoplimit" && (
                    <>
                      {/* Quantity Input */}
                      <Box mb="m">
                        <Box
                          flexDirection="row"
                          justifyContent="space-between"
                          alignItems="center"
                          mb="xs"
                        >
                          <Box flexDirection="row" alignItems="center">
                            <Text
                              variant="contentSmall"
                              color="caption_text_secondary"
                              fontFamily={AlbertSans.SEMI_BOLD}
                            >
                              Quantity
                            </Text>
                            <TouchableOpacity style={{ marginLeft: 4 }}>
                              <SvgIcon icon={icons.ic_refresh} size={15} />
                            </TouchableOpacity>
                          </Box>
                          <Text variant="caption" color="caption_text_secondary">
                            1 Lot
                          </Text>
                        </Box>
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="input_Border"
                          borderRadius="s"
                          backgroundColor="cardBg"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { textAlign: "center", color: colors.headline }]}
                            placeholder="Enter quantity"

                            placeholderTextColor={colors.headline}
                            value={size}
                            onChangeText={(value) => handleChange("size", value)}
                            onBlur={() => handleBlur("size")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(size || "0");
                              if (currentValue > 0) {
                                handleChange("size", (currentValue - 1).toString());
                              }
                            }}
                          >
                            <Text style={{ fontSize: 20, color: colors.headline }}>−</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(size || "0");
                              handleChange("size", (currentValue + 1).toString());
                            }}
                          >
                            <Text style={{ fontSize: 20, color: colors.headline }}>+</Text>
                          </TouchableOpacity>
                        </Box>
                      </Box>

                      {/* Limit Price Input */}
                      <Box mb="m">
                        <Box
                          flexDirection="row"
                          justifyContent="space-between"
                          alignItems="center"
                          mb="xs"
                        >
                          <Box flexDirection="row" alignItems="center">
                            <Text
                              variant="contentSmall"
                              color="caption_text_secondary"
                              fontFamily={AlbertSans.SEMI_BOLD}
                            >
                              Limit Price
                            </Text>
                            <TouchableOpacity style={{ marginLeft: 4 }}>
                              <SvgIcon icon={icons.ic_refresh} size={15} />
                            </TouchableOpacity>
                          </Box>
                        </Box>
                        <Box position="relative">
                          <Box
                            flexDirection="row"
                            borderWidth={1}
                            borderColor="input_Border"
                            borderRadius="m"
                            backgroundColor="cardBg"
                            overflow="hidden"
                          >
                            <TextInput
                              style={[styles.styledInput, { textAlign: 'center', color: colors.headline }]}
                              placeholder="Enter limit price"
                              placeholderTextColor={colors.headline}
                              value={price}
                              onChangeText={(value) =>
                                handleChange("price", value)
                              }
                              onBlur={() => handleBlur("price")}
                              keyboardType="numeric"
                            />
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(price || "0");
                                if (currentValue > 0) {
                                  const newValue = (currentValue - (security?.tick_size || 0.1)).toFixed(1);
                                  handleChange("price", newValue);
                                }
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(price || "0");
                                const newValue = (currentValue + (security?.tick_size || 0.1)).toFixed(1);
                                handleChange("price", newValue);
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                            </TouchableOpacity>
                          </Box>
                        </Box>
                      </Box>

                      {/* Stop Price Input */}
                      <Box mb="m">
                        <Box
                          flexDirection="row"
                          justifyContent="space-between"
                          alignItems="center"
                          mb="xs"
                        >
                          <Box flexDirection="row" alignItems="center">
                            <Text
                              variant="contentSmall"
                              color="caption_text_secondary"
                              fontFamily={AlbertSans.SEMI_BOLD}
                            >
                              Stop Price
                            </Text>
                            <TouchableOpacity style={{ marginLeft: 4 }}>
                              <SvgIcon icon={icons.ic_refresh} size={15} />
                            </TouchableOpacity>
                          </Box>
                        </Box>
                        <Box position="relative">
                          <Box
                            flexDirection="row"
                            borderWidth={1}
                            borderColor="outline"
                            borderRadius="m"
                            backgroundColor="white"
                            overflow="hidden"
                          >
                            <TextInput
                              style={[styles.styledInput, { textAlign: 'center', color: colors.headline }]}
                              placeholder="Enter stop price"
                              value={stopPrice}
                              onChangeText={(value) =>
                                handleChange("stopPrice", value)
                              }
                              onBlur={() => handleBlur("stopPrice")}
                              keyboardType="numeric"
                            />
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(stopPrice || "0");
                                if (currentValue > 0) {
                                  const newValue = (currentValue - (security?.tick_size || 0.1)).toFixed(1);
                                  handleChange("stopPrice", newValue);
                                }
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(stopPrice || "0");
                                const newValue = (currentValue + (security?.tick_size || 0.1)).toFixed(1);
                                handleChange("stopPrice", newValue);
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                            </TouchableOpacity>
                          </Box>
                        </Box>
                      </Box>

                      {/* Stop Loss Input */}
                      <Box mb="m">
                        <Box
                          flexDirection="row"
                          alignItems="center"
                          justifyContent="space-between"
                          mb="xs"
                        >
                          <Text
                            variant="contentSmall"
                            color="caption_text_secondary"
                            fontFamily={AlbertSans.SEMI_BOLD}
                          >
                            Stop Loss
                          </Text>
                          <ToggleSwitch
                            isEnabled={stopLossEnabled}
                            onToggle={() =>
                              handleChange("stopLossEnabled", !stopLossEnabled)
                            }
                            trackColor={{ false: "#E9E9E9", true: "#4CD964" }}
                          />
                        </Box>
                        {stopLossEnabled && (
                          <Box position="relative">
                            <Box
                              flexDirection="row"
                              borderWidth={1}
                              borderColor="outline"
                              borderRadius="m"
                              backgroundColor="white"
                              overflow="hidden"
                            >
                              <TextInput
                                style={[styles.styledInput, { color: colors.headline }]}
                                placeholder="Enter stop loss"
                                value={stopLoss}
                                onChangeText={(value) =>
                                  handleChange("stopLoss", value)
                                }
                                onBlur={() => handleBlur("stopLoss")}
                                keyboardType="numeric"
                              />
                              <TouchableOpacity
                                style={styles.priceTypeButton}
                                onPress={() => handlePriceTypePress("stopLoss")}
                              >
                                <Box flexDirection="row" alignItems="center">
                                  <Text variant="body" color="primary_purple" mr="xs">
                                    {stopLossPriceType}
                                  </Text>
                                  <SvgIcon
                                    icon={icons.ic_arrow_down}
                                    size={16}
                                    color={palette.primary_purple}
                                  />
                                </Box>
                              </TouchableOpacity>
                            </Box>
                            {showPriceTypePopup && activeField === "stopLoss" && (
                              <PriceTypePopup
                                selectedType={stopLossPriceType}
                                onSelectType={handleSelectPriceType}
                                onClose={() => setShowPriceTypePopup(false)}
                              />
                            )}
                          </Box>
                        )}
                      </Box>

                      {/* Iceberg Legs Input */}
                      <Box mb="m">
                        <Text
                          variant="contentSmall"
                          color="caption_text_secondary"
                          fontFamily={AlbertSans.SEMI_BOLD}
                          mb="xs"
                        >
                          Iceberg Legs
                        </Text>
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="outline"
                          borderRadius="m"
                          backgroundColor="white"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { textAlign: "center", color: colors.headline }]}
                            placeholder="Enter iceberg legs"
                            value={icebergLegs}
                            onChangeText={(value) => handleChange("icebergLegs", value)}
                            onBlur={() => handleBlur("icebergLegs")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(icebergLegs || "0");
                              if (currentValue > 0) {
                                handleChange("icebergLegs", (currentValue - 1).toString());
                              }
                            }}
                          >
                            <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseInt(icebergLegs || "0");
                              handleChange("icebergLegs", (currentValue + 1).toString());
                            }}
                          >
                            <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                          </TouchableOpacity>
                        </Box>
                      </Box>
                    </>
                  )}

                  {/* Size Input for Limit and Stop orders */}
                  {(orderType === "limit" || orderType === "stop") && (
                    <Box mb="m">
                      <Box
                        flexDirection="row"
                        justifyContent="space-between"
                        alignItems="center"
                        mb="xs"
                      >
                        <Box flexDirection="row" alignItems="center">
                          <Text
                            variant="contentSmall"
                            color="caption_text_secondary"
                            fontFamily={AlbertSans.SEMI_BOLD}
                          >
                            Quantity
                          </Text>
                          <TouchableOpacity style={{ marginLeft: 4 }}>
                            <SvgIcon icon={icons.ic_refresh} size={15} />
                          </TouchableOpacity>
                        </Box>
                        <Text variant="caption" color="caption_text_secondary">
                          1 Lot
                        </Text>
                      </Box>
                      <Box
                        flexDirection="row"
                        borderWidth={1}
                        borderColor="input_Border"
                        borderRadius="s"
                        backgroundColor="cardBg"
                        overflow="hidden"
                      >
                        <TextInput
                          style={[styles.styledInput, { textAlign: "center", color: colors.headline }]}
                          placeholder="Enter quantity"
                          placeholderTextColor={colors.headline}
                          value={size}
                          onChangeText={(value) => handleChange("size", value)}
                          onBlur={() => handleBlur("size")}
                          keyboardType="numeric"
                        />
                        <TouchableOpacity
                          style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                          onPress={() => {
                            const currentValue = parseInt(size || "0");
                            if (currentValue > 0) {
                              handleChange("size", (currentValue - 1).toString());
                            }
                          }}
                        >
                          <Text style={{ fontSize: 20, color: colors.headline }}>−</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                          onPress={() => {
                            const currentValue = parseInt(size || "0");
                            handleChange("size", (currentValue + 1).toString());
                          }}
                        >
                          <Text style={{ fontSize: 20, color: colors.headline }}>+</Text>
                        </TouchableOpacity>
                      </Box>
                    </Box>
                  )}

                  {/* Price Input (for Limit orders) */}
                  {orderType === "limit" && (
                    <Box mb="m">
                      <Box
                        flexDirection="row"
                        justifyContent="space-between"
                        alignItems="center"
                        mb="xs"
                      >
                        <Box flexDirection="row" alignItems="center">
                          <Text
                            variant="contentSmall"
                            color="caption_text_secondary"
                            fontFamily={AlbertSans.SEMI_BOLD}
                          >
                            Limit Price
                          </Text>
                          <TouchableOpacity style={{ marginLeft: 4 }}>
                            <SvgIcon icon={icons.ic_refresh} size={15} />
                          </TouchableOpacity>
                        </Box>
                      </Box>
                      <Box position="relative">
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="input_Border"
                          borderRadius="m"
                          backgroundColor="cardBg"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { textAlign: 'center', color: colors.headline }]}
                            placeholder="Enter limit price"
                            placeholderTextColor={colors.headline}
                            value={price}
                            onChangeText={(value) => handleChange("price", value)}
                            onBlur={() => handleBlur("price")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseFloat(price || "0");
                              if (currentValue > 0) {
                                const newValue = (currentValue - (security?.tick_size || 0.1)).toFixed(1);
                                handleChange("price", newValue);
                              }
                            }}
                          >
                            <Text style={{ fontSize: 20, color: colors.headline }}>−</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseFloat(price || "0");
                              const newValue = (currentValue + (security?.tick_size || 0.1)).toFixed(1);
                              handleChange("price", newValue);
                            }}
                          >
                            <Text style={{ fontSize: 20, color: colors.headline }}>+</Text>
                          </TouchableOpacity>

                        </Box>

                      </Box>
                    </Box>
                  )}

                  {/* Stop Price Input (for Stop orders) */}
                  {orderType === "stop" && (
                    <Box mb="m">
                      <Box flexDirection="row" justifyContent="space-between" alignItems="center" mb="xs">
                        <Box flexDirection="row" alignItems="center">
                          <Text
                            variant="contentSmall"
                            color="caption_text_secondary"
                            fontFamily={AlbertSans.SEMI_BOLD}
                          >
                            Stop Price
                          </Text>
                          <TouchableOpacity style={{ marginLeft: 4 }}>
                            <SvgIcon icon={icons.ic_refresh} size={15} />
                          </TouchableOpacity>
                        </Box>
                      </Box>
                      <Box position="relative">
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="outline"
                          borderRadius="m"
                          backgroundColor="white"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { textAlign: 'center', color: colors.headline }]}
                            placeholder="Enter stop price"
                            value={stopPrice}
                            onChangeText={(value) =>
                              handleChange("stopPrice", value)
                            }
                            onBlur={() => handleBlur("stopPrice")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseFloat(stopPrice || "0");
                              if (currentValue > 0) {
                                const newValue = (currentValue - (security?.tick_size || 0.1)).toFixed(1);
                                handleChange("stopPrice", newValue);
                              }
                            }}
                          >
                            <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                            onPress={() => {
                              const currentValue = parseFloat(stopPrice || "0");
                              const newValue = (currentValue + (security?.tick_size || 0.1)).toFixed(1);
                              handleChange("stopPrice", newValue);
                            }}
                          >
                            <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                          </TouchableOpacity>
                        </Box>
                      </Box>
                    </Box>
                  )}

                  {/* Stop Limit (Price) Toggle and Input */}
                  {orderType === "stop" && (
                    <Box mb="m">
                      <Box
                        flexDirection="row"
                        alignItems="center"
                        justifyContent="space-between"
                        mb="m"
                      >
                        <Text
                          variant="contentSmall"
                          color="caption_text_secondary"
                          fontFamily={AlbertSans.SEMI_BOLD}
                        >
                          Stop Limit (Price)
                        </Text>
                        <ToggleSwitch
                          isEnabled={stopLimitEnabled}
                          onToggle={() =>
                            handleChange("stopLimitEnabled", !stopLimitEnabled)
                          }
                          trackColor={{ false: "#E9E9E9", true: "#4CD964" }}
                        />
                      </Box>
                      {stopLimitEnabled && (
                        <Box position="relative">
                          <Box
                            flexDirection="row"
                            borderWidth={1}
                            borderColor="outline"
                            borderRadius="m"
                            backgroundColor="white"
                            overflow="hidden"
                          >
                            <TextInput
                              style={[styles.styledInput, { textAlign: 'center', color: colors.headline }]}
                              placeholder="Enter stop limit price"
                              value={stopLimit}
                              onChangeText={(value) =>
                                handleChange("stopLimit", value)
                              }
                              onBlur={() => handleBlur("stopLimit")}
                              keyboardType="numeric"
                            />
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(stopLimit || "0");
                                if (currentValue > 0) {
                                  const newValue = (currentValue - (security?.tick_size || 0.1)).toFixed(1);
                                  handleChange("stopLimit", newValue);
                                }
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>−</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[styles.quantityControlButton, { borderLeftColor: colors.input_Border }]}
                              onPress={() => {
                                const currentValue = parseFloat(stopLimit || "0");
                                const newValue = (currentValue + (security?.tick_size || 0.1)).toFixed(1);
                                handleChange("stopLimit", newValue);
                              }}
                            >
                              <Text style={{ fontSize: 20, color: palette.text }}>+</Text>
                            </TouchableOpacity>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  )}

                  {/* Take Profit Input */}
                  <Box mb="m">
                    <Box
                      flexDirection="row"
                      alignItems="center"
                      justifyContent="space-between"
                      mb="m"
                    >
                      <Text
                        variant="contentSmall"
                        color="caption_text_secondary"
                        fontFamily={AlbertSans.SEMI_BOLD}
                      >
                        Take Profit
                      </Text>
                      <ToggleSwitch
                        isEnabled={takeProfitEnabled}
                        onToggle={() =>
                          handleChange("takeProfitEnabled", !takeProfitEnabled)
                        }
                        trackColor={{ false: "#E9E9E9", true: "#4CD964" }}
                      />
                    </Box>
                    {takeProfitEnabled && (
                      <Box position="relative">
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="outline"
                          borderRadius="m"
                          backgroundColor="white"
                          overflow="hidden"
                        >
                          <TextInput
                            style={[styles.styledInput, { color: colors.headline }]}
                            placeholder="Enter take profit"
                            value={takeProfit}
                            onChangeText={(value) =>
                              handleChange("takeProfit", value)
                            }
                            onBlur={() => handleBlur("takeProfit")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={styles.priceTypeButton}
                            onPress={() => handlePriceTypePress("takeProfit")}
                          >
                            <Box flexDirection="row" alignItems="center">
                              <Text variant="body" color="primary_purple" mr="xs">
                                {takeProfitPriceType}
                              </Text>
                              <SvgIcon
                                icon={icons.ic_arrow_down}
                                size={16}
                                color={palette.primary_purple}
                              />
                            </Box>
                          </TouchableOpacity>
                        </Box>
                        {showPriceTypePopup && activeField === "takeProfit" && (
                          <PriceTypePopup
                            selectedType={takeProfitPriceType}
                            onSelectType={handleSelectPriceType}
                            onClose={() => setShowPriceTypePopup(false)}
                          />
                        )}
                      </Box>
                    )}
                  </Box>

                  {/* Stop Loss Input */}
                  <Box mb="m">
                    <Box
                      flexDirection="row"
                      alignItems="center"
                      justifyContent="space-between"
                      mb="m"
                    >
                      <Text
                        variant="contentSmall"
                        color="caption_text_secondary"
                        fontFamily={AlbertSans.SEMI_BOLD}
                      >
                        Stop Loss
                      </Text>
                      <ToggleSwitch
                        isEnabled={stopLossEnabled}
                        onToggle={() =>
                          handleChange("stopLossEnabled", !stopLossEnabled)
                        }
                        trackColor={{ false: "#E9E9E9", true: "#4CD964" }}
                      />
                    </Box>
                    {stopLossEnabled && (
                      <Box position="relative">
                        <Box
                          flexDirection="row"
                          borderWidth={1}
                          borderColor="outline"
                          borderRadius="m"
                          backgroundColor="white"
                          overflow="hidden"
                        >
                          <TextInput
                            style={styles.styledInput}
                            placeholder="Enter stop loss"
                            value={stopLoss}
                            onChangeText={(value) =>
                              handleChange("stopLoss", value)
                            }
                            onBlur={() => handleBlur("stopLoss")}
                            keyboardType="numeric"
                          />
                          <TouchableOpacity
                            style={styles.priceTypeButton}
                            onPress={() => handlePriceTypePress("stopLoss")}
                          >
                            <Box flexDirection="row" alignItems="center">
                              <Text variant="body" color="primary_purple" mr="xs">
                                {stopLossPriceType}
                              </Text>
                              <SvgIcon
                                icon={icons.ic_arrow_down}
                                size={16}
                                color={palette.primary_purple}
                              />
                            </Box>
                          </TouchableOpacity>
                        </Box>
                        {showPriceTypePopup && activeField === "stopLoss" && (
                          <PriceTypePopup
                            selectedType={stopLossPriceType}
                            onSelectType={handleSelectPriceType}
                            onClose={() => setShowPriceTypePopup(false)}
                          />
                        )}
                      </Box>
                    )}
                  </Box>

                  {/* Iceberg Legs Input */}
                  <Box mb="m">
                    <Text variant="contentSmall"
                      color="caption_text_secondary" mb="xs">
                      Iceberg Legs
                    </Text>
                    <Box flexDirection="row" alignItems="center">
                      <TextInput
                        style={[styles.input, { flex: 1, backgroundColor: colors.cardBg, color: colors.headline }]}
                        placeholder="Enter iceberg legs"
                        value={icebergLegs}
                        onChangeText={(value) => handleChange("icebergLegs", value)}
                        onBlur={() => handleBlur("icebergLegs")}
                        keyboardType="numeric"
                      />
                      <Box flexDirection="row" ml="s">
                        <TouchableOpacity
                          style={[styles.quantityButton, { backgroundColor: colors.cardBg, borderColor: colors.input_Border }]}
                          onPress={() => {
                            const currentValue = parseInt(icebergLegs || "0");
                            if (currentValue > 0) {
                              handleChange(
                                "icebergLegs",
                                (currentValue - 1).toString()
                              );
                            }
                          }}
                        >
                          <Text style={[styles.quantityButtonText, { color: colors.headline }]}>−</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.quantityButton, { backgroundColor: colors.cardBg, borderColor: colors.input_Border }]}
                          onPress={() => {
                            const currentValue = parseInt(icebergLegs || "0");
                            handleChange(
                              "icebergLegs",
                              (currentValue + 1).toString()
                            );
                          }}
                        >
                          <Text style={[styles.quantityButtonText, { color: colors.headline }]}>+</Text>
                        </TouchableOpacity>
                      </Box>
                    </Box>
                  </Box>

                  {/* Market Depth Section */}
                  <Box mb="m">
                    <Text variant="caption" color="caption_text_primary" mb="xs">
                      MARKET DEPTH
                    </Text>
                    <Box
                      backgroundColor="cardBg"
                      borderRadius="m"
                      borderWidth={1}
                      borderColor="input_Border"
                    >
                      <Box
                        flexDirection="row"
                        borderBottomWidth={1}
                        borderBottomColor="input_Border"
                      >
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Bid
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Sell
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Price
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Buy
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Ask
                          </Text>
                        </Box>
                      </Box>

                      {/* Market Depth Rows */}
                      {[249.5, 249.6, 249.7, 249.8, 249.9].map((price, index) => (
                        <Box key={index} flexDirection="row">
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption">{index < 3 ? "0.03" : ""}</Text>
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption">3.09</Text>
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption" color="negative">
                              {price.toFixed(2)}
                            </Text>
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption">3.09</Text>
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption">
                              {index > 2 ? "0.03" : "0"}
                            </Text>
                          </Box>
                        </Box>
                      ))}

                      <TouchableOpacity>
                        <Box p="s" alignItems="center">
                          <Text variant="caption" color="primary_purple">
                            + Show 20 depth
                          </Text>
                        </Box>
                      </TouchableOpacity>
                    </Box>
                  </Box>

                  {/* Time & Sales Section */}
                  <Box mb="m">
                    <Text variant="caption" color="caption_text_primary" mb="xs">
                      TIME & SALES
                    </Text>
                    <Box
                      backgroundColor="cardBg"
                      borderRadius="m"
                      borderWidth={1}
                      borderColor="input_Border"
                    >
                      <Box
                        flexDirection="row"
                        borderBottomWidth={1}
                        borderBottomColor="input_Border"
                      >
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Side
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Size
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Price
                          </Text>
                        </Box>
                        <Box flex={1} p="s" alignItems="center">
                          <Text variant="caption" color="caption_text_primary">
                            Time
                          </Text>
                        </Box>
                      </Box>

                      {/* Time & Sales Rows */}
                      {[
                        {
                          side: "buy",
                          size: "0.00479",
                          price: "259.78",
                          time: "12:45:32",
                        },
                        {
                          side: "buy",
                          size: "0.00479",
                          price: "259.78",
                          time: "08:15:07",
                        },
                        {
                          side: "buy",
                          size: "0.00479",
                          price: "259.78",
                          time: "23:59:59",
                        },
                        {
                          side: "sell",
                          size: "0.00479",
                          price: "259.77",
                          time: "06:30:45",
                        },
                        {
                          side: "buy",
                          size: "0.00479",
                          price: "259.78",
                          time: "14:22:10",
                        },
                      ].map((item, index) => (
                        <Box
                          key={index}
                          flexDirection="row"
                          style={{
                            backgroundColor:
                              item.side === "sell"
                                ? "rgba(255, 105, 96, 0.1)"
                                : "cardBg",
                          }}
                        >
                          <Box flex={1} p="s" alignItems="center">
                            <SvgIcon
                              icon={
                                item.side === "buy"
                                  ? icons.ic_chevron_right
                                  : icons.ic_chevron_right
                              }
                              size={16}
                              color={
                                item.side === "buy"
                                  ? palette.primary_green
                                  : palette.negative
                              }
                            />
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption">{item.size}</Text>
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text
                              variant="caption"
                              color={
                                item.side === "buy" ? "primary_green" : "negative"
                              }
                            >
                              {item.price}
                            </Text>
                          </Box>
                          <Box flex={1} p="s" alignItems="center">
                            <Text variant="caption">{item.time}</Text>
                          </Box>
                        </Box>
                      ))}

                      <TouchableOpacity>
                        <Box p="s" alignItems="center">
                          <Text variant="caption" color="primary_purple">
                            + Show 20 Sales
                          </Text>
                        </Box>
                      </TouchableOpacity>
                    </Box>
                  </Box>

                  {/* See More Options */}
                  <TouchableOpacity>
                    <Box
                      flexDirection="row"
                      justifyContent="space-between"
                      alignItems="center"
                      mb="m"
                    >
                      <Text variant="contentSmall" fontFamily={AlbertSans.SEMI_BOLD}>
                        See More Options
                      </Text>
                      <SvgIcon
                        icon={icons.ic_chevron_right}
                        size={20}
                        color={palette.text}
                      />
                    </Box>
                  </TouchableOpacity>

                  {/* Place Trade Buttons */}
                  <Box flexDirection="row" mb="xl">
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() => {
                        handleChange("side", "buy");
                        handlePlaceTrade();
                      }}
                      style={[styles.placeTradeButton, styles.buyPlaceButton]}
                    >
                      <Text style={styles.placeTradeButtonText}>
                        BUY
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      activeOpacity={0.7}
                      onPress={() => {
                        handleChange("side", "sell");
                        handlePlaceTrade();
                      }}
                      style={[styles.placeTradeButton, styles.sellPlaceButton]}
                    >
                      <Text style={styles.placeTradeButtonText}>
                        SELL
                      </Text>
                    </TouchableOpacity>
                  </Box>
                </Box>
              </ScrollView>
            </Box>
          </Box>
        </SafeAreaView>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 't',
  },
  modalContent: {
    // backgroundColor: palette.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  input: {
    // backgroundColor: palette.card_table_tile_header,
    borderRadius: 8,
    padding: spacing.m,
    fontFamily: AlbertSans.REGULAR,
    fontSize: 16,
  },
  styledInput: {
    flex: 1,
    padding: spacing.m,
    fontFamily: AlbertSans.SEMI_BOLD,
    fontSize: 18,
    // color: palette.text,
  },
  priceTypeButton: {
    paddingHorizontal: spacing.m,
    justifyContent: "center",
    borderLeftWidth: 1,
    borderLeftColor: palette.outline,
  },
  quantityControlButton: {
    width: 50,
    justifyContent: "center",
    alignItems: "center",
    borderLeftWidth: 1,
    // borderLeftColor: palette.outline,
  },
  quantityButton: {
    width: 40,
    height: 40,
    // backgroundColor: palette.white,
    borderWidth: 1,
    // borderColor: palette.outline,
    borderRadius: 4,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 2,
  },
  quantityButtonText: {
    fontSize: 20,
    // color: palette.text,
  },
  sideButton: {
    flex: 1,
    padding: spacing.m,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 4,
  },
  buyButton: {
    backgroundColor: "rgba(34, 171, 148, 0.1)",
    borderWidth: 1,
    borderColor: palette.primary_green,
  },
  sellButton: {
    backgroundColor: "rgba(255, 105, 96, 0.1)",
    borderWidth: 1,
    borderColor: palette.negative,
  },

  sideButtonText: {
    fontFamily: AlbertSans.SEMI_BOLD,
    fontSize: 16,
    color: palette.primary_green, // Will be overridden in component
  },

  orderTypeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 8,
  },
  tabScrollView: {
    maxHeight: 60,
    minHeight: 60,
    marginBottom: spacing.m,
  },
  tabScrollContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 0,
    paddingVertical: 8,
    flexGrow: 0,
  },
  placeTradeButton: {
    flex: 1,
    padding: spacing.m,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 4,
    height: 50,
  },
  buyPlaceButton: {
    backgroundColor: palette.primary_green,
  },
  sellPlaceButton: {
    backgroundColor: palette.negative,
  },
  placeTradeButtonText: {
    fontFamily: AlbertSans.BOLD,
    fontSize: 16,
    color: palette.white,
  },
});

export default TradeModal;
