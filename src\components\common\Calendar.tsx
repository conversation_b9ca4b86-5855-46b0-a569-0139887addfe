import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { palette } from '@/theme/theme';
import AntDesign from '@expo/vector-icons/AntDesign';

interface CalendarProps {
  onSelectDate: (date: Date) => void;
  initialDate?: Date;
  minDate?: Date;
}

const Calendar: React.FC<CalendarProps> = ({ onSelectDate, initialDate, minDate }) => {
  const [currentMonth, setCurrentMonth] = useState(initialDate || new Date());
  const [selectedDate, setSelectedDate] = useState(initialDate || new Date());

  // Days of the week
  const daysOfWeek = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

  // Get the first day of the month
  const getFirstDayOfMonth = (date: Date) => {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    // Convert Sunday (0) to 6 to match our calendar layout (Monday is 0)
    return firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1;
  };

  // Get the number of days in the month
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  // Get the previous month's days that appear in the current month view
  const getPreviousMonthDays = (date: Date) => {
    const firstDayOfMonth = getFirstDayOfMonth(date);
    const previousMonth = new Date(date.getFullYear(), date.getMonth() - 1);
    const daysInPreviousMonth = getDaysInMonth(previousMonth);

    const days = [];
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(daysInPreviousMonth - firstDayOfMonth + i + 1);
    }
    return days;
  };

  // Get the next month's days that appear in the current month view
  const getNextMonthDays = (date: Date) => {
    const firstDayOfMonth = getFirstDayOfMonth(date);
    const daysInMonth = getDaysInMonth(date);
    const totalCells = 42; // 6 rows x 7 columns
    const remainingCells = totalCells - (firstDayOfMonth + daysInMonth);

    const days = [];
    for (let i = 1; i <= remainingCells; i++) {
      days.push(i);
    }
    return days;
  };

  // Format the month and year for display
  const formatMonthYear = (date: Date) => {
    return date.toLocaleString('default', { month: 'long', year: 'numeric' });
  };

  // Navigate to the previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  // Navigate to the next month
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  // Check if a date is the selected date
  const isSelectedDate = (day: number) => {
    return (
      selectedDate.getDate() === day &&
      selectedDate.getMonth() === currentMonth.getMonth() &&
      selectedDate.getFullYear() === currentMonth.getFullYear()
    );
  };

  // Check if a date is today
  const isToday = (day: number) => {
    const today = new Date();
    return (
      today.getDate() === day &&
      today.getMonth() === currentMonth.getMonth() &&
      today.getFullYear() === currentMonth.getFullYear()
    );
  };

  // Check if a date is before the minimum date
  const isBeforeMinDate = (day: number) => {
    if (!minDate) return false;

    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    return date < minDate;
  };

  // Handle date selection
  const handleSelectDate = (day: number) => {
    if (isBeforeMinDate(day)) return;

    const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);

    // Update local state
    setSelectedDate(newDate);

    // Immediately notify parent component
    console.log("Calendar: Selected date", newDate);
    onSelectDate(newDate);
  };

  // Render the calendar grid
  const renderCalendarGrid = () => {
    const firstDayOfMonth = getFirstDayOfMonth(currentMonth);
    const daysInMonth = getDaysInMonth(currentMonth);
    const previousMonthDays = getPreviousMonthDays(currentMonth);
    const nextMonthDays = getNextMonthDays(currentMonth);

    const rows = [];
    let cells = [];

    // Previous month days
    previousMonthDays.forEach((day) => {
      cells.push(
        <TouchableOpacity
          key={`prev-${day}`}
          style={styles.dayCell}
          disabled={true}
        >
          <Text style={styles.inactiveDay}>{day}</Text>
        </TouchableOpacity>
      );
    });

    // Current month days
    for (let day = 1; day <= daysInMonth; day++) {
      const isSelected = isSelectedDate(day);
      const isTodayDate = isToday(day);
      const isDisabled = isBeforeMinDate(day);

      cells.push(
        <TouchableOpacity
          key={`current-${day}`}
          style={[
            styles.dayCell,
            isSelected && styles.selectedDay,
            isTodayDate && styles.todayCell
          ]}
          onPress={() => handleSelectDate(day)}
          disabled={isDisabled}
        >
          <Text style={[
            styles.dayText,
            isSelected && styles.selectedDayText,
            isDisabled && styles.disabledDayText,
            (day === 6 || day === 7 || day === 13 || day === 14 || day === 20 || day === 21 || day === 27 || day === 28) && styles.weekendDay
          ]}>
            {day}
          </Text>
        </TouchableOpacity>
      );

      // Start a new row after every 7 cells
      if ((firstDayOfMonth + day) % 7 === 0 || day === daysInMonth) {
        rows.push(
          <View key={`row-${rows.length}`} style={styles.calendarRow}>
            {cells}
          </View>
        );
        cells = [];
      }
    }

    // Next month days
    nextMonthDays.forEach((day) => {
      cells.push(
        <TouchableOpacity
          key={`next-${day}`}
          style={styles.dayCell}
          disabled={true}
        >
          <Text style={styles.inactiveDay}>{day}</Text>
        </TouchableOpacity>
      );

      // Start a new row after every 7 cells
      if (cells.length === 7) {
        rows.push(
          <View key={`row-${rows.length}`} style={styles.calendarRow}>
            {cells}
          </View>
        );
        cells = [];
      }
    });

    // Add any remaining cells as a row
    if (cells.length > 0) {
      rows.push(
        <View key={`row-${rows.length}`} style={styles.calendarRow}>
          {cells}
        </View>
      );
    }

    return rows;
  };

  return (
    <View style={styles.container}>
      {/* Calendar Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => goToPreviousMonth()} style={styles.navButton}>
          <Text style={styles.navButtonText}>«</Text>
        </TouchableOpacity>

        <Text style={styles.monthYearText}>{formatMonthYear(currentMonth)}</Text>

        <TouchableOpacity onPress={() => goToNextMonth()} style={styles.navButton}>
          <Text style={styles.navButtonText}>»</Text>
        </TouchableOpacity>
      </View>

      {/* Days of Week */}
      <View style={styles.daysOfWeekContainer}>
        {daysOfWeek.map((day, index) => (
          <View key={index} style={styles.dayOfWeekCell}>
            <Text style={[
              styles.dayOfWeekText,
              (index === 5 || index === 6) && styles.weekendDayOfWeek
            ]}>
              {day}
            </Text>
          </View>
        ))}
      </View>

      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {renderCalendarGrid()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 10,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingHorizontal: 10,
  },
  navButton: {
    padding: 5,
  },
  navButtonText: {
    fontSize: 20,
    color: palette.primary100,
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: '500',
    color: palette.primary100,
  },
  daysOfWeekContainer: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  dayOfWeekCell: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 5,
  },
  dayOfWeekText: {
    fontSize: 12,
    fontWeight: '500',
    color: palette.primary100,
  },
  weekendDayOfWeek: {
    color: 'red',
  },
  calendarGrid: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  calendarRow: {
    flexDirection: 'row',
  },
  dayCell: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dayText: {
    fontSize: 14,
    color: palette.headline1,
  },
  inactiveDay: {
    fontSize: 14,
    color: '#ccc',
  },
  selectedDay: {
    backgroundColor: palette.primary100,
    borderRadius: 20,
    width: 36,
    height: 36,
  },
  selectedDayText: {
    color: 'white',
    fontWeight: '500',
  },
  todayCell: {
    borderWidth: 1,
    borderColor: palette.primary100,
    borderRadius: 20,
    width: 36,
    height: 36,
  },
  disabledDayText: {
    color: '#ccc',
  },
  weekendDay: {
    color: 'red',
  },
});

export default Calendar;
