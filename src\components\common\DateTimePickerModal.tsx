import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView } from 'react-native';
import { palette, Theme } from '@/theme/theme';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Calendar from './Calendar';
import { useTheme } from '@shopify/restyle';

interface DateTimePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectDateTime: (date: Date) => void;
  initialDate?: Date;
  minDate?: Date;
  mode?: 'date' | 'time' | 'datetime';
}

const DateTimePickerModal: React.FC<DateTimePickerModalProps> = ({
  visible,
  onClose,
  onSelectDateTime,
  initialDate = new Date(),
  minDate,
  mode = 'datetime'
}) => {
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [currentView, setCurrentView] = useState<'date' | 'time'>(mode === 'time' ? 'time' : 'date');

  // For time selection
  const [selectedHours, setSelectedHours] = useState(initialDate.getHours());
  const [selectedMinutes, setSelectedMinutes] = useState(initialDate.getMinutes());
  const [period, setPeriod] = useState(initialDate.getHours() >= 12 ? 'PM' : 'AM');

  // References for ScrollViews
  const hourScrollViewRef = React.useRef<ScrollView>(null);
  const minuteScrollViewRef = React.useRef<ScrollView>(null);

  // Reset state when modal becomes visible
  useEffect(() => {
    if (visible) {
      setSelectedDate(initialDate);
      setSelectedHours(initialDate.getHours());
      setSelectedMinutes(initialDate.getMinutes());
      setPeriod(initialDate.getHours() >= 12 ? 'PM' : 'AM');
      setCurrentView(mode === 'time' ? 'time' : 'date');
    }
  }, [visible, initialDate, mode]);

  // Scroll to the selected hour and minute when the time view is shown
  useEffect(() => {
    if (visible && currentView === 'time') {
      // Small delay to ensure the ScrollView is rendered
      const timer = setTimeout(() => {
        if (hourScrollViewRef.current) {
          const displayHour = period === 'AM'
            ? (selectedHours === 0 ? 12 : selectedHours)
            : (selectedHours === 12 ? 12 : selectedHours - 12);
          const hourOffset = (displayHour - 1) * 44; // Approximate height of each item
          hourScrollViewRef.current.scrollTo({ y: hourOffset, animated: true });
        }

        if (minuteScrollViewRef.current) {
          const minuteOffset = selectedMinutes * 44;
          minuteScrollViewRef.current.scrollTo({ y: minuteOffset, animated: true });
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [visible, currentView, selectedHours, selectedMinutes, period]);

  // Generate hours (1-12)
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);

  // Generate all 60 minutes (0-59)
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    // Create a new date with the selected date but keep the current time
    const newDate = new Date(date);
    newDate.setHours(selectedHours);
    newDate.setMinutes(selectedMinutes);

    setSelectedDate(newDate);

    if (mode === 'date') {
      // If mode is date only, complete the selection
      onSelectDateTime(newDate);
      onClose();
    } else {
      // Otherwise, move to time selection
      setCurrentView('time');
    }
  };

  // Handle hour selection
  const handleHourSelect = (hour: number) => {
    // Convert from 12-hour to 24-hour format
    const hours24 = period === 'AM'
      ? (hour === 12 ? 0 : hour)
      : (hour === 12 ? 12 : hour + 12);

    setSelectedHours(hours24);

    // Update the selected date
    const newDate = new Date(selectedDate);
    newDate.setHours(hours24);
    setSelectedDate(newDate);

    // Scroll to the selected hour
    if (hourScrollViewRef.current) {
      const offset = (hour - 1) * 44; // Approximate height of each item
      hourScrollViewRef.current.scrollTo({ y: offset, animated: true });
    }
  };

  // Handle minute selection
  const handleMinuteSelect = (minute: number) => {
    setSelectedMinutes(minute);

    // Update the selected date
    const newDate = new Date(selectedDate);
    newDate.setMinutes(minute);
    setSelectedDate(newDate);

    // Scroll to the selected minute
    if (minuteScrollViewRef.current) {
      const offset = minute * 44; // Approximate height of each item
      minuteScrollViewRef.current.scrollTo({ y: offset, animated: true });
    }
  };

  // Handle period selection (AM/PM)
  const handlePeriodSelect = (newPeriod: 'AM' | 'PM') => {
    if (newPeriod === period) return;

    setPeriod(newPeriod);

    // Update hours based on the new period
    let newHours = selectedHours;
    if (newPeriod === 'AM' && selectedHours >= 12) {
      newHours = selectedHours - 12;
    } else if (newPeriod === 'PM' && selectedHours < 12) {
      newHours = selectedHours + 12;
    }

    setSelectedHours(newHours);

    // Update the selected date
    const newDate = new Date(selectedDate);
    newDate.setHours(newHours);
    setSelectedDate(newDate);
  };

  // Handle confirm button press
  const handleConfirm = () => {
    onSelectDateTime(selectedDate);
    onClose();
  };

  // Format time for display
  const formatTime = (date: Date) => {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  // Format date for display
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  const { colors } = useTheme<Theme>();
  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={[styles.pickerContainer, { backgroundColor: colors.backgroundColor }]}>
          {/* Header */}
          <View style={styles.header}>
            {currentView === 'time' && mode !== 'time' && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => setCurrentView('date')}
              >
                <MaterialIcons name="arrow-back" size={24} color={palette.primary100} />
              </TouchableOpacity>
            )}
            <Text style={[styles.headerText, { color: colors.headline }]}>
              {currentView === 'date' ? 'Select Date' : 'Select Time'}
            </Text>
            <View style={styles.backButton} />
          </View>

          {/* Current Selection Display */}
          <View style={styles.selectionDisplay}>
            {mode !== 'time' && (
              <TouchableOpacity
                style={[
                  styles.selectionItem,
                  currentView === 'date' && styles.activeSelectionItem
                ]}
                onPress={() => setCurrentView('date')}
              >
                <Text style={styles.selectionText}>
                  {formatDate(selectedDate)}
                </Text>
              </TouchableOpacity>
            )}

            {mode !== 'date' && (
              <TouchableOpacity
                style={[
                  styles.selectionItem,
                  currentView === 'time' && styles.activeSelectionItem
                ]}
                onPress={() => setCurrentView('time')}
              >
                <Text style={styles.selectionText}>
                  {formatTime(selectedDate)}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Date View */}
          {currentView === 'date' && (
            <View style={styles.dateContainer}>
              <Calendar
                onSelectDate={handleDateSelect}
                initialDate={selectedDate}
                minDate={minDate}
              />
            </View>
          )}

          {/* Time View */}
          {currentView === 'time' && (
            <View style={styles.timeContainer}>
              <View style={styles.timeSelectionContainer}>
                {/* Hours */}
                <View style={styles.columnContainer}>
                  <Text style={[styles.columnHeader, { color: colors.headline }]}>Hour</Text>
                  <ScrollView
                    ref={hourScrollViewRef}
                    style={styles.scrollView}
                    showsVerticalScrollIndicator={true}
                  >
                    {hours.map((hour) => {
                      const displayHour = period === 'AM'
                        ? (selectedHours === 0 ? 12 : selectedHours)
                        : (selectedHours === 12 ? 12 : selectedHours - 12);
                      const isSelected = hour === displayHour;

                      return (
                        <TouchableOpacity
                          key={`hour-${hour}`}
                          style={[
                            styles.timeOption,
                            isSelected && styles.selectedTimeOption
                          ]}
                          onPress={() => handleHourSelect(hour)}
                        >
                          <Text
                            style={[
                              styles.timeOptionText,
                              isSelected && styles.selectedTimeOptionText
                            ]}
                          >
                            {hour}
                          </Text>
                        </TouchableOpacity>
                      );
                    })}
                  </ScrollView>
                </View>

                {/* Minutes */}
                <View style={styles.columnContainer}>
                  <Text style={[styles.columnHeader, { color: colors.headline }]}>Minute</Text>
                  <ScrollView
                    ref={minuteScrollViewRef}
                    style={styles.scrollView}
                    showsVerticalScrollIndicator={true}
                  >
                    {minutes.map((minute) => (
                      <TouchableOpacity
                        key={`minute-${minute}`}
                        style={[
                          styles.timeOption,
                          minute === selectedMinutes && styles.selectedTimeOption
                        ]}
                        onPress={() => handleMinuteSelect(minute)}
                      >
                        <Text
                          style={[
                            styles.timeOptionText,
                            minute === selectedMinutes && styles.selectedTimeOptionText
                          ]}
                        >
                          {minute.toString().padStart(2, '0')}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>

                {/* AM/PM */}
                <View style={styles.columnContainer}>
                  <Text style={[styles.columnHeader, { color: colors.headline }]}>Period</Text>
                  <View style={styles.periodContainer}>
                    <TouchableOpacity
                      style={[
                        styles.periodOption,
                        period === 'AM' && styles.selectedPeriodOption
                      ]}
                      onPress={() => handlePeriodSelect('AM')}
                    >
                      <Text
                        style={[
                          styles.periodOptionText,
                          { color: colors.headline },
                          period === 'AM' && styles.selectedPeriodOptionText
                        ]}
                      >
                        AM
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        styles.periodOption,
                        period === 'PM' && styles.selectedPeriodOption
                      ]}
                      onPress={() => handlePeriodSelect('PM')}
                    >
                      <Text
                        style={[
                          styles.periodOptionText,
                          period === 'PM' && styles.selectedPeriodOptionText
                        ]}
                      >
                        PM
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          )}

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            {(currentView === 'time' || mode === 'date') && (
              <TouchableOpacity
                style={[styles.button, styles.confirmButton]}
                onPress={handleConfirm}
              >
                <Text style={styles.confirmButtonText}>Confirm</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContainer: {
    width: '90%',
    // backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '500',
    color: palette.headline1,
    textAlign: 'center',
    flex: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectionDisplay: {
    flexDirection: 'row',
    width: '100%',
    marginBottom: 20,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: palette.splitterLine,
  },
  selectionItem: {
    flex: 1,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: palette.headerBg,
  },
  activeSelectionItem: {
    backgroundColor: palette.primary100,
  },
  selectionText: {
    fontSize: 16,
    fontWeight: '500',
    color: palette.headline1,
  },
  dateContainer: {
    width: '100%',
    marginBottom: 20,
  },
  timeContainer: {
    width: '100%',
    marginBottom: 20,
  },
  timeSelectionContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  columnContainer: {
    flex: 1,
    alignItems: 'center',
  },
  columnHeader: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
  scrollView: {
    height: 200,
    width: '100%',
  },
  timeOption: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 2,
    borderRadius: 5,
  },
  selectedTimeOption: {
    backgroundColor: palette.primary100,
  },
  timeOptionText: {
    fontSize: 16,
    color: palette.headline1,
  },
  selectedTimeOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  periodContainer: {
    width: '100%',
  },
  periodOption: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 5,
    borderRadius: 5,
  },
  selectedPeriodOption: {
    backgroundColor: palette.primary100,
  },
  periodOptionText: {
    fontSize: 16,
    color: palette.headline1,
  },
  selectedPeriodOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    padding: 10,
    borderRadius: 5,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: palette.headerBg,
    marginRight: 10,
    flex: 1,
  },
  confirmButton: {
    backgroundColor: palette.primary100,
    flex: 1,
  },
  cancelButtonText: {
    color: palette.headline1,
  },
  confirmButtonText: {
    color: 'white',
  },
});

export default DateTimePickerModal;
