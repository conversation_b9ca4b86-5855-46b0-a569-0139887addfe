import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView } from 'react-native';
import { palette } from '@/theme/theme';

interface TimePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectTime: (hours: number, minutes: number) => void;
  initialHours?: number;
  initialMinutes?: number;
}

const TimePickerModal: React.FC<TimePickerModalProps> = ({
  visible,
  onClose,
  onSelectTime,
  initialHours = 12,
  initialMinutes = 0
}) => {
  const [selectedHours, setSelectedHours] = useState(initialHours);
  const [selectedMinutes, setSelectedMinutes] = useState(initialMinutes);
  const [period, setPeriod] = useState(initialHours >= 12 ? 'PM' : 'AM');

  // References for ScrollViews
  const hourScrollViewRef = React.useRef<ScrollView>(null);
  const minuteScrollViewRef = React.useRef<ScrollView>(null);

  // Scroll to the selected hour and minute when the component mounts
  useEffect(() => {
    // Small delay to ensure the ScrollView is rendered
    const timer = setTimeout(() => {
      if (hourScrollViewRef.current) {
        const hourOffset = ((selectedHours === 0 ? 12 : selectedHours % 12 || 12) - 1) * 44;
        hourScrollViewRef.current.scrollTo({ y: hourOffset, animated: true });
      }

      if (minuteScrollViewRef.current) {
        const minuteOffset = selectedMinutes * 44;
        minuteScrollViewRef.current.scrollTo({ y: minuteOffset, animated: true });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [selectedHours, selectedMinutes, visible]);

  // Generate hours (1-12)
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);

  // Generate all 60 minutes (0-59)
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  // Handle hour selection
  const handleHourSelect = (hour: number) => {
    setSelectedHours(period === 'AM' ? (hour === 12 ? 0 : hour) : (hour === 12 ? 12 : hour + 12));

    // Scroll to the selected hour
    if (hourScrollViewRef.current) {
      const offset = (hour - 1) * 44; // Approximate height of each item
      hourScrollViewRef.current.scrollTo({ y: offset, animated: true });
    }
  };

  // Handle minute selection
  const handleMinuteSelect = (minute: number) => {
    setSelectedMinutes(minute);

    // Scroll to the selected minute
    if (minuteScrollViewRef.current) {
      const offset = minute * 44; // Approximate height of each item
      minuteScrollViewRef.current.scrollTo({ y: offset, animated: true });
    }
  };

  // Handle period selection (AM/PM)
  const handlePeriodSelect = (newPeriod: 'AM' | 'PM') => {
    setPeriod(newPeriod);
    if (newPeriod === 'AM' && selectedHours >= 12) {
      setSelectedHours(selectedHours - 12);
    } else if (newPeriod === 'PM' && selectedHours < 12) {
      setSelectedHours(selectedHours + 12);
    }
  };

  // Handle confirm button press
  const handleConfirm = () => {
    // Convert 12-hour format to 24-hour format
    const hours24 = period === 'AM'
      ? (selectedHours === 12 ? 0 : selectedHours)
      : (selectedHours === 12 ? 12 : selectedHours + 12);

    onSelectTime(hours24, selectedMinutes);
    onClose();
  };

  // Format time for display
  const formatTime = (hours: number, minutes: number, period: string) => {
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  };

  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.timePickerContainer}>
          <View style={styles.header}>
            <Text style={styles.headerText}>Select Time</Text>
          </View>

          <View style={styles.timeDisplay}>
            <Text style={styles.timeText}>
              {formatTime(
                period === 'AM' ? (selectedHours === 12 ? 0 : selectedHours) : (selectedHours === 12 ? 12 : selectedHours + 12),
                selectedMinutes,
                period
              )}
            </Text>
          </View>

          <View style={styles.selectionContainer}>
            {/* Hours */}
            <View style={styles.columnContainer}>
              <Text style={styles.columnHeader}>Hour</Text>
              <ScrollView
                ref={hourScrollViewRef}
                style={styles.scrollView}
                showsVerticalScrollIndicator={true}
              >
                {hours.map((hour) => (
                  <TouchableOpacity
                    key={`hour-${hour}`}
                    style={[
                      styles.timeOption,
                      (period === 'AM' && (hour === 12 ? 0 : hour) === selectedHours) ||
                        (period === 'PM' && (hour === 12 ? 12 : hour + 12) === selectedHours)
                        ? styles.selectedTimeOption
                        : null
                    ]}
                    onPress={() => handleHourSelect(hour)}
                  >
                    <Text
                      style={[
                        styles.timeOptionText,
                        (period === 'AM' && (hour === 12 ? 0 : hour) === selectedHours) ||
                          (period === 'PM' && (hour === 12 ? 12 : hour + 12) === selectedHours)
                          ? styles.selectedTimeOptionText
                          : null
                      ]}
                    >
                      {hour}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Minutes */}
            <View style={styles.columnContainer}>
              <Text style={styles.columnHeader}>Minute</Text>
              <ScrollView
                ref={minuteScrollViewRef}
                style={styles.scrollView}
                showsVerticalScrollIndicator={true}
              >
                {minutes.map((minute) => (
                  <TouchableOpacity
                    key={`minute-${minute}`}
                    style={[
                      styles.timeOption,
                      minute === selectedMinutes ? styles.selectedTimeOption : null
                    ]}
                    onPress={() => handleMinuteSelect(minute)}
                  >
                    <Text
                      style={[
                        styles.timeOptionText,
                        minute === selectedMinutes ? styles.selectedTimeOptionText : null
                      ]}
                    >
                      {minute.toString().padStart(2, '0')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* AM/PM */}
            <View style={styles.columnContainer}>
              <Text style={styles.columnHeader}>Period</Text>
              <View style={styles.periodContainer}>
                <TouchableOpacity
                  style={[
                    styles.periodOption,
                    period === 'AM' ? styles.selectedPeriodOption : null
                  ]}
                  onPress={() => handlePeriodSelect('AM')}
                >
                  <Text
                    style={[
                      styles.periodOptionText,
                      period === 'AM' ? styles.selectedPeriodOptionText : null
                    ]}
                  >
                    AM
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.periodOption,
                    period === 'PM' ? styles.selectedPeriodOption : null
                  ]}
                  onPress={() => handlePeriodSelect('PM')}
                >
                  <Text
                    style={[
                      styles.periodOptionText,
                      period === 'PM' ? styles.selectedPeriodOptionText : null
                    ]}
                  >
                    PM
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={handleConfirm}
            >
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  timePickerContainer: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '500',
    color: palette.headline1,
  },
  timeDisplay: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
    padding: 10,
    backgroundColor: palette.headerBg,
    borderRadius: 8,
  },
  timeText: {
    fontSize: 24,
    fontWeight: '500',
    color: palette.primary100,
  },
  selectionContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  columnContainer: {
    flex: 1,
    alignItems: 'center',
  },
  columnHeader: {
    fontSize: 14,
    fontWeight: '500',
    color: palette.headline1,
    marginBottom: 10,
  },
  scrollView: {
    height: 150,
    width: '100%',
  },
  timeOption: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 2,
    borderRadius: 5,
  },
  selectedTimeOption: {
    backgroundColor: palette.primary100,
  },
  timeOptionText: {
    fontSize: 16,
    color: palette.headline1,
  },
  selectedTimeOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  periodContainer: {
    width: '100%',
  },
  periodOption: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 5,
    borderRadius: 5,
  },
  selectedPeriodOption: {
    backgroundColor: palette.primary100,
  },
  periodOptionText: {
    fontSize: 16,
    color: palette.headline1,
  },
  selectedPeriodOptionText: {
    color: 'white',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  button: {
    padding: 10,
    borderRadius: 5,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: palette.headerBg,
    marginRight: 10,
  },
  confirmButton: {
    backgroundColor: palette.primary100,
  },
  cancelButtonText: {
    color: palette.headline1,
  },
  confirmButtonText: {
    color: 'white',
  },
});

export default TimePickerModal;
