import React from 'react';
import { StyleSheet, TouchableOpacity, View, Switch } from 'react-native';
import { palette } from '@/theme/theme';

interface ToggleSwitchProps {
  isEnabled: boolean;
  onToggle: () => void;
  trackColor?: {
    false: string;
    true: string;
  };
  thumbColor?: {
    false: string;
    true: string;
  };
}

export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  isEnabled,
  onToggle,
  trackColor = { false: '#E0E0E0', true: '#00C48C' },
  thumbColor = { false: '#FFFFFF', true: '#FFFFFF' },
}) => {
  return (
    <View style={styles.customSwitch}>
      <View
        style={[styles.switchTrack, isEnabled ? styles.switchTrackActive : styles.switchTrackInactive]}
      >
        <View
          style={[styles.switchThumb, isEnabled ? styles.switchThumbActive : styles.switchThumbInactive]}
        />
      </View>
      <Switch
        value={isEnabled}
        onValueChange={onToggle}
        trackColor={{ false: 'transparent', true: 'transparent' }}
        thumbColor="transparent"
        style={{ opacity: 0, position: 'absolute', width: '100%', height: '100%' }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  customSwitch: {
    width: 50,
    height: 25,
    justifyContent: 'center',
    position: 'relative',
  },
  switchTrack: {
    width: 50,
    height: 30,
    borderRadius: 15,
    padding: 2,
  },
  switchTrackActive: {
    backgroundColor: '#00C48C',
  },
  switchTrackInactive: {
    backgroundColor: palette.captionText2,
  },
  switchThumb: {
    width: 25,
    height: 25,
    borderRadius: 13,
    backgroundColor: 'white',
  },
  switchThumbActive: {
    alignSelf: 'flex-end',
  },
  switchThumbInactive: {
    alignSelf: 'flex-start',
  },
});
