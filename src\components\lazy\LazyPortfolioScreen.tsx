import React, { Suspense, lazy } from 'react';
import TabSkeleton from '@/components/skeletons/TabSkeleton';

// Lazy load the portfolio screen
const PortfolioScreen = lazy(() => import('@/app/(tabs)/(my-portfolio)/index'));

const LazyPortfolioScreen = () => {
  return (
    <Suspense fallback={<TabSkeleton type="portfolio" />}>
      <PortfolioScreen />
    </Suspense>
  );
};

export default LazyPortfolioScreen;
