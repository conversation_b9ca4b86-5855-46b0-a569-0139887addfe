import React, { Suspense, lazy } from 'react';
import TabSkeleton from '@/components/skeletons/TabSkeleton';

// Lazy load the watchlist screen
const WatchlistScreen = lazy(() => import('@/app/(tabs)/(watchlist)/index'));

const LazyWatchlistScreen = () => {
  return (
    <Suspense fallback={<TabSkeleton type="watchlist" />}>
      <WatchlistScreen />
    </Suspense>
  );
};

export default LazyWatchlistScreen;
