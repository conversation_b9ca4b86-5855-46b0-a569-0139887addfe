import React, { useState, useCallback, useEffect } from 'react';
import { Tabs } from 'expo-router';
import { Platform } from 'react-native';
import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useTabPreloader } from '@/hooks/useTabOptimization';
import { measureTabSwitch, performanceTracker } from '@/utils/performanceTest';

// Icons
import ChartIcon from '@/assets/icons/chart.svg';
import Briefcase from '@/assets/icons/briefcase.svg';
import Widgets from '@/assets/icons/widgets.svg';
import User from '@/assets/icons/user.svg';
import Home from '@/assets/icons/home.svg';

interface OptimizedTabLayoutProps {
  children: React.ReactNode;
}

const TAB_NAMES = ['(watchlist)', '(charts)', '(my-portfolio)', '(widgets)', '(account)'];

const OptimizedTabLayout: React.FC<OptimizedTabLayoutProps> = ({ children }) => {
  const [activeTab, setActiveTab] = useState('(watchlist)');
  const [tabSwitchMeasurement, setTabSwitchMeasurement] = useState<any>(null);
  
  const { shouldPreload } = useTabPreloader(activeTab, TAB_NAMES);

  // Handle tab press with performance measurement
  const handleTabPress = useCallback((tabName: string) => {
    if (tabName === activeTab) return;

    // Start measuring tab switch performance
    const measurement = measureTabSwitch(activeTab, tabName);
    setTabSwitchMeasurement(measurement);
    
    // Update active tab
    setActiveTab(tabName);
    
    // Complete measurement after a short delay to account for rendering
    setTimeout(() => {
      if (measurement) {
        measurement.complete();
      }
    }, 100);
  }, [activeTab]);

  // Log performance report periodically in development
  useEffect(() => {
    if (__DEV__) {
      const interval = setInterval(() => {
        const report = performanceTracker.generateReport();
        if (report.includes('|')) { // Only log if there's actual data
          console.log(report);
        }
      }, 30000); // Every 30 seconds

      return () => clearInterval(interval);
    }
  }, []);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: "black",
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            position: "absolute",
          },
          default: {},
        }),
      }}
    >
      <Tabs.Screen
        name='index'
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name='(watchlist)'
        options={{
          title: "Watchlist",
          tabBarIcon: ({ color }) => <Home />,
        }}
        listeners={{
          tabPress: () => handleTabPress('(watchlist)'),
        }}
      />
      <Tabs.Screen
        name='(charts)'
        options={{
          title: "Charts",
          tabBarIcon: () => <ChartIcon />,
        }}
        listeners={{
          tabPress: () => handleTabPress('(charts)'),
        }}
      />
      <Tabs.Screen
        name='(my-portfolio)'
        options={{
          title: "My Portfolio",
          tabBarIcon: ({ color }) => <Briefcase />,
        }}
        listeners={{
          tabPress: () => handleTabPress('(my-portfolio)'),
        }}
      />
      <Tabs.Screen
        name='(widgets)'
        options={{
          title: "Widgets",
          tabBarIcon: ({ color }) => <Widgets />,
        }}
        listeners={{
          tabPress: () => handleTabPress('(widgets)'),
        }}
      />
      <Tabs.Screen
        name='(account)'
        options={{
          title: "Account",
          tabBarIcon: ({ color }) => <User />,
        }}
        listeners={{
          tabPress: () => handleTabPress('(account)'),
        }}
      />
    </Tabs>
  );
};

export default OptimizedTabLayout;
