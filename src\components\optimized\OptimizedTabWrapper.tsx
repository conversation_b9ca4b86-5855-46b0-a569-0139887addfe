import React, { memo, useEffect, useState } from 'react';
import { InteractionManager } from 'react-native';
import { useTabOptimization } from '@/hooks/useTabOptimization';
import TabSkeleton from '@/components/skeletons/TabSkeleton';

interface OptimizedTabWrapperProps {
  children: React.ReactNode;
  tabName: string;
  skeletonType: 'charts' | 'watchlist' | 'portfolio' | 'widgets' | 'account';
  isActive?: boolean;
  enableOptimization?: boolean;
}

const OptimizedTabWrapper: React.FC<OptimizedTabWrapperProps> = memo(({
  children,
  tabName,
  skeletonType,
  isActive = false,
  enableOptimization = true,
}) => {
  const [shouldRenderContent, setShouldRenderContent] = useState(!enableOptimization || isActive);
  const { isReady, shouldRender, forceRender } = useTabOptimization(tabName);

  useEffect(() => {
    if (isActive && enableOptimization) {
      // Force immediate rendering for active tab
      forceRender();
      setShouldRenderContent(true);
    } else if (!isActive && enableOptimization) {
      // Defer rendering for inactive tabs
      const task = InteractionManager.runAfterInteractions(() => {
        setShouldRenderContent(shouldRender);
      });
      return () => task.cancel();
    }
  }, [isActive, enableOptimization, shouldRender, forceRender]);

  // Always render immediately if optimization is disabled
  if (!enableOptimization) {
    return <>{children}</>;
  }

  // Show skeleton while content is loading
  if (!shouldRenderContent || !isReady) {
    return <TabSkeleton type={skeletonType} />;
  }

  return <>{children}</>;
});

OptimizedTabWrapper.displayName = 'OptimizedTabWrapper';

export default OptimizedTabWrapper;
