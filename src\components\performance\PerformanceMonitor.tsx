import React, { useEffect, useRef } from 'react';
import { InteractionManager, Platform } from 'react-native';

interface PerformanceMonitorProps {
  componentName: string;
  enableLogging?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  componentName,
  enableLogging = __DEV__,
}) => {
  const mountTime = useRef<number>(Date.now());
  const renderCount = useRef<number>(0);
  const lastRenderTime = useRef<number>(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    lastRenderTime.current = Date.now();

    if (enableLogging) {
      const renderTime = Date.now() - mountTime.current;
      console.log(`[Performance] ${componentName} - Render #${renderCount.current} - Time: ${renderTime}ms`);
    }
  });

  useEffect(() => {
    mountTime.current = Date.now();
    
    if (enableLogging) {
      console.log(`[Performance] ${componentName} - Component mounted`);
    }

    // Monitor interaction completion
    const task = InteractionManager.runAfterInteractions(() => {
      const interactionTime = Date.now() - mountTime.current;
      if (enableLogging) {
        console.log(`[Performance] ${componentName} - Interactions completed in ${interactionTime}ms`);
      }
    });

    return () => {
      const unmountTime = Date.now() - mountTime.current;
      if (enableLogging) {
        console.log(`[Performance] ${componentName} - Component unmounted after ${unmountTime}ms`);
      }
      task.cancel();
    };
  }, [componentName, enableLogging]);

  return null;
};

export default PerformanceMonitor;
