import React from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Box } from '@/components/restyle';
import { useTheme } from '@shopify/restyle';
import { Theme } from '@/theme/theme';

interface TabSkeletonProps {
  type: 'charts' | 'watchlist' | 'portfolio' | 'widgets' | 'account';
}

const TabSkeleton: React.FC<TabSkeletonProps> = ({ type }) => {
  const theme = useTheme<Theme>();
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();
    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const SkeletonBox = ({ width, height, style }: any) => (
    <Animated.View
      style={[
        {
          width,
          height,
          backgroundColor: theme.colors.card_table_tile_header,
          borderRadius: 8,
          opacity,
        },
        style,
      ]}
    />
  );

  const renderChartsSkeleton = () => (
    <Box flex={1} backgroundColor="backgroundColor" p="m">
      {/* Header */}
      <SkeletonBox width="60%" height={24} style={{ marginBottom: 16 }} />
      
      {/* Chart area */}
      <Box flex={1} flexDirection="row" mb="m">
        <Box flex={0.8} mr="s">
          <SkeletonBox width="100%" height="100%" />
        </Box>
        <Box flex={0.2}>
          {[...Array(8)].map((_, i) => (
            <SkeletonBox key={i} width="100%" height={30} style={{ marginBottom: 8 }} />
          ))}
        </Box>
      </Box>
      
      {/* Toolbar */}
      <Box flexDirection="row" justifyContent="space-between" alignItems="center" height={56}>
        {[...Array(5)].map((_, i) => (
          <SkeletonBox key={i} width={40} height={40} />
        ))}
      </Box>
    </Box>
  );

  const renderWatchlistSkeleton = () => (
    <Box flex={1} backgroundColor="backgroundColor" p="m">
      {/* Header */}
      <Box flexDirection="row" justifyContent="space-between" mb="l">
        <Box>
          <SkeletonBox width={100} height={16} style={{ marginBottom: 8 }} />
          <SkeletonBox width={150} height={20} />
        </Box>
        <SkeletonBox width={40} height={40} />
      </Box>
      
      {/* Popular stocks */}
      <SkeletonBox width={120} height={20} style={{ marginBottom: 16 }} />
      <Box flexDirection="row" mb="l">
        {[...Array(3)].map((_, i) => (
          <SkeletonBox key={i} width={100} height={80} style={{ marginRight: 12 }} />
        ))}
      </Box>
      
      {/* Stocks list */}
      <SkeletonBox width={80} height={20} style={{ marginBottom: 16 }} />
      {[...Array(6)].map((_, i) => (
        <Box key={i} flexDirection="row" justifyContent="space-between" alignItems="center" mb="m">
          <Box flexDirection="row" alignItems="center" flex={1}>
            <SkeletonBox width={40} height={40} style={{ marginRight: 12 }} />
            <Box flex={1}>
              <SkeletonBox width="70%" height={16} style={{ marginBottom: 4 }} />
              <SkeletonBox width="50%" height={12} />
            </Box>
          </Box>
          <Box alignItems="flex-end">
            <SkeletonBox width={60} height={16} style={{ marginBottom: 4 }} />
            <SkeletonBox width={40} height={12} />
          </Box>
        </Box>
      ))}
    </Box>
  );

  const renderDefaultSkeleton = () => (
    <Box flex={1} backgroundColor="backgroundColor" justifyContent="center" alignItems="center" p="l">
      <SkeletonBox width={200} height={24} style={{ marginBottom: 24 }} />
      <SkeletonBox width="80%" height={16} style={{ marginBottom: 16 }} />
      <SkeletonBox width="60%" height={16} style={{ marginBottom: 16 }} />
      <SkeletonBox width="70%" height={16} />
    </Box>
  );

  switch (type) {
    case 'charts':
      return renderChartsSkeleton();
    case 'watchlist':
      return renderWatchlistSkeleton();
    default:
      return renderDefaultSkeleton();
  }
};

export default TabSkeleton;
