import React, { memo, useState, useCallback } from "react";
import { Image, ImageProps, ImageSourcePropType, View } from "react-native";
import { Box } from "@/components/restyle";
import { spacing } from "@/theme/theme";

interface OptimizedImageProps
	extends Omit<ImageProps, "source" | "borderRadius"> {
	source: ImageSourcePropType;
	fallbackSource?: ImageSourcePropType;
	placeholder?: React.ReactNode;
	width?: number;
	height?: number;
	/**
	 * Border radius value. Can be:
	 * - Theme spacing key (e.g., "xs", "s", "m", "mx", "l", "xl") - shows autocomplete suggestions
	 * - Custom number (e.g., 12, 18, 25) - for precise control
	 * @example
	 * borderRadius="m"     // Uses theme spacing.m (16px)
	 * borderRadius={12}    // Uses custom 12px radius
	 */
	borderRadius?: keyof typeof spacing | (number & {});
	lazy?: boolean;
}

const OptimizedImage = memo<OptimizedImageProps>(
	({
		source,
		fallbackSource,
		placeholder,
		width = 37,
		height = 37,
		borderRadius = "mx",
		lazy = true,
		style,
		...props
	}) => {
		const [isLoading, setIsLoading] = useState(lazy);
		const [hasError, setHasError] = useState(false);
		const [isVisible, setIsVisible] = useState(!lazy);

		const handleLoad = useCallback(() => {
			setIsLoading(false);
		}, []);

		const handleError = useCallback(() => {
			setHasError(true);
			setIsLoading(false);
		}, []);

		const handleLayout = useCallback(() => {
			if (lazy && !isVisible) {
				setIsVisible(true);
			}
		}, [lazy, isVisible]);

		// Convert theme spacing key to numeric value or use custom number
		const borderRadiusValue = borderRadius
			? typeof borderRadius === "string"
				? spacing[borderRadius as keyof typeof spacing]
				: borderRadius
			: 0;

		const imageStyle = [
			{
				width,
				height,
				borderRadius: borderRadiusValue,
			},
			style,
		];

		// Show placeholder while loading or if lazy loading hasn't started
		if (isLoading || !isVisible) {
			return (
				<View style={imageStyle} onLayout={handleLayout}>
					{placeholder || (
						<Box
							width={width}
							height={height}
							borderRadius={
								typeof borderRadius === "string"
									? borderRadius
									: undefined
							}
							backgroundColor='borderGray'
							style={
								typeof borderRadius === "number"
									? { borderRadius }
									: undefined
							}
						/>
					)}
				</View>
			);
		}

		// Show fallback image if main image failed to load
		if (hasError && fallbackSource) {
			return (
				<Image
					source={fallbackSource}
					style={imageStyle}
					onLoad={handleLoad}
					{...props}
				/>
			);
		}

		// Show main image
		return (
			<Image
				source={source}
				style={imageStyle}
				onLoad={handleLoad}
				onError={handleError}
				{...props}
			/>
		);
	}
);

OptimizedImage.displayName = "OptimizedImage";

export default OptimizedImage;
