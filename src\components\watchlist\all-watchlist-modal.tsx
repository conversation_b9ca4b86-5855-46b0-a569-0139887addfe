import { icons } from "@/assets/icons";
import { Backdrop, Spacer, SvgIcon } from "@/components";
import { Box, Text } from "@/components/restyle";
import { Scaffold } from "@/components/scaffold";
import { showToast } from "@/services/toast";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useAddWatchlistMutation } from "@/store/services/watchlist-api";
import { setNewWatchlistItem } from "@/store/reducers/watchlist.reducer";
import { AlbertSans, Theme } from "@/theme/theme";
import { ISettingsDataType } from "@/types/request";
import { IWatchlistDataItem } from "@/types";
import { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import shortid from "shortid";
import BottomSheet, { BottomSheetFlatList } from "@gorhom/bottom-sheet";
import { hp } from "@/utils/responsive";
import { WatchListItem } from "@/components/watchlist";
import { useTheme } from "@shopify/restyle";

export interface AllWatchlistModalRef {
	expand: () => void;
	close: () => void;
}

interface AllWatchlistModalProps {
	watchlistName: string;
	setWatchlistName: (name: string) => void;
}

const AllWatchlistModal = forwardRef<AllWatchlistModalRef, AllWatchlistModalProps>(
	({ watchlistName, setWatchlistName }, ref) => {
		const { colors } = useTheme<Theme>();
		const dispatch = useAppDispatch();
		const watchlistList = useAppSelector((state) => state.watchlistList);

		// Initialize bottom sheet ref
		const bottomSheetRef = useRef<BottomSheet>(null);

		// Setup API mutations
		const [addWatchlist] = useAddWatchlistMutation();

		// Memoize bottom sheet snap points
		const snapPoints = useMemo(() => [hp(60)], []);

		// Expose methods to parent component
		useImperativeHandle(ref, () => ({
			expand: () => bottomSheetRef.current?.expand(),
			close: () => bottomSheetRef.current?.close(),
		}));

		// Handle creating a new watchlist
		const addNewWatchlist = useCallback(async () => {
			if (!watchlistName.trim()) {
				showToast("Please enter a watchlist name");
				return;
			}

			try {
				const key = shortid();
				const res = await addWatchlist({
					key,
					action: "INSERT",
					name: watchlistName,
					data_type: ISettingsDataType.WATCHLIST,
					value: {
						objects: [],
					},
				}).unwrap();

				if (res.status === 200) {
					showToast(`Watchlist ${watchlistName} Created!`);
					setWatchlistName("");
					bottomSheetRef.current?.close();
				}
			} catch (error) {
				showToast("Failed to create watchlist. Please try again.");
			}
		}, [watchlistName, addWatchlist, setWatchlistName]);

		// Handle watchlist item selection
		const handleWatchlistItemPress = useCallback(
			(item: IWatchlistDataItem) => {
				console.log("Setting Watchlist: ", item);
				dispatch(setNewWatchlistItem(item));
				bottomSheetRef.current?.close();
			},
			[dispatch]
		);

		return (
			<BottomSheet
				ref={bottomSheetRef}
				index={-1}
				snapPoints={snapPoints}
				enablePanDownToClose
				backgroundStyle={{
					backgroundColor: colors.backgroundColor,
				}}
				backdropComponent={Backdrop}
			>
				<Box pb='xl' flex={1}>
					<Box py='m'>
						<Text variant='secondaryHeader' textAlign='center'>
							{watchlistList?.length} Watchlists
						</Text>
					</Box>
					<Spacer size={10} />
					<TouchableOpacity
						activeOpacity={0.9}
						onPress={addNewWatchlist}
					>
						<Box
							backgroundColor='primary_purple'
							p='mx'
							flexDirection='row'
							alignItems='center'
							justifyContent='space-between'
						>
							<Text
								color='white'
								fontSize={14}
								fontFamily={AlbertSans.SEMI_BOLD}
							>
								Create New list
							</Text>
							<SvgIcon icon={icons.ic_add_white} size={20} />
						</Box>
					</TouchableOpacity>
					<Scaffold>
						<BottomSheetFlatList
							data={watchlistList}
							renderItem={(props) => (
								<WatchListItem
									{...props}
									onPress={handleWatchlistItemPress}
								/>
							)}
							keyExtractor={(item) => item?.key}
							contentContainerStyle={styles.bsFlatList}
							style={{
								flexGrow: 0,
							}}
						/>
					</Scaffold>
				</Box>
			</BottomSheet>
		);
	}
);

AllWatchlistModal.displayName = "AllWatchlistModal";

const styles = StyleSheet.create({
	bsFlatList: {
		paddingBottom: 100,
	},
});

export default AllWatchlistModal;
