import { Box, Text } from "@/components/restyle";
import { AlbertSans } from "@/theme/theme";
import { FC, memo, useCallback } from "react";
import { TouchableOpacity } from "react-native";

interface OptimizedMenuItemProps {
	item: Record<string, string>;
	index: number;
	isActive: boolean;
	onPress: (item: Record<string, string>) => void;
}

const OptimizedMenuItem: FC<OptimizedMenuItemProps> = memo(
	({ item, isActive, onPress }) => {
		const handlePress = useCallback(() => {
			onPress?.(item);
		}, [onPress, item]);

		return (
			<TouchableOpacity activeOpacity={0.5} onPress={handlePress}>
				<Box
					px='mx'
					py='s'
					mr='m'
					backgroundColor={
						isActive ? "headline_primary" : "splitter_line"
					}
					borderRadius='m'
				>
					<Text
						color={isActive ? "white" : "headline_primary"}
						fontFamily={AlbertSans.BOLD}
					>
						{item?.label}
					</Text>
				</Box>
			</TouchableOpacity>
		);
	},
	// Custom comparison function
	(prevProps, nextProps) => {
		return (
			prevProps.item?.label === nextProps.item?.label &&
			prevProps.isActive === nextProps.isActive
		);
	}
);

OptimizedMenuItem.displayName = "OptimizedMenuItem";

export default OptimizedMenuItem;
