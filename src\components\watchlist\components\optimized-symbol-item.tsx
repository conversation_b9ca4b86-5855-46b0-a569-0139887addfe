import { icons } from "@/assets/icons";
import { Box, Text } from "@/components/restyle";
import { SvgIcon } from "@/components/svg-icon";
import { showToast } from "@/services/toast";
import { useAppSelector } from "@/store/hooks";
import {
	isSymbolAvailable,
	selectWatchlistKey,
	selectWatchlistName,
} from "@/store/reducers/watchlist.reducer";
import { useAddSymbolMutation } from "@/store/services/watchlist-api";
import { AlbertSans, palette } from "@/theme/theme";
import { ISettingsDataType } from "@/types/request";
import { ISearchSymbolResult } from "@/types/response";
import { getRandomColor, shrinkName } from "@/utils/functions";
import { ListRenderItemInfo } from "@shopify/flash-list";
import { FC, useMemo, useCallback, memo, useState } from "react";
import { ActivityIndicator, TouchableOpacity } from "react-native";

const OptimizedSymbolItem: FC<ListRenderItemInfo<ISearchSymbolResult>> = memo(
	({ item: symbolItem }) => {
		const watchlistKey = useAppSelector(selectWatchlistKey);
		const watchlistName = useAppSelector(selectWatchlistName);

		// Use optimized selector for O(1) symbol availability check
		const symbolAdded = useAppSelector((state) =>
			isSymbolAvailable(
				state,
				`${symbolItem?.item?.exchange}:${symbolItem?.item?.segment}:${symbolItem?.item?.symbol}`
			)
		);

		const [symbolExists, setSymbolExists] = useState<boolean>(symbolAdded);

		// API hooks
		const [addSymbol, { isLoading: addSymbolLoading }] =
			useAddSymbolMutation();

		const randomColor = useMemo(() => getRandomColor(), []);

		const symbolName = useMemo(
			() => symbolItem?.item?.name?.charAt(0) ?? "?",
			[symbolItem?.item?.name]
		);

		const shrunkName = useMemo(
			() => shrinkName(symbolItem?.item?.name, 20),
			[symbolItem?.item?.name]
		);

		// Memoized add symbol function
		const addSymbolToWatchlist = useCallback(async () => {
			try {
				const res = await addSymbol({
					action: "WATCHLIST_ADD",
					data_type: ISettingsDataType.WATCHLIST,
					key: watchlistKey,
					name: watchlistName,
					value: {
						settings: { savedToCloud: true },
						objects: [
							{
								exchange: symbolItem?.item?.exchange,
								segment: symbolItem?.item?.segment,
								source: symbolItem?.item?.data_source_location,
								symbol: symbolItem?.item?.symbol,
								ticker: symbolItem?.item?.key,
							},
						],
					},
				}).unwrap();

				if (res.status === 200) {
					// showToast("Symbol added to watchlist", "success");
					setSymbolExists(true);
				}
			} catch (error) {
				console.error("Error adding symbol:", error);
				showToast("Failed to add symbol", "error");
			}
		}, [addSymbol, watchlistName, watchlistKey, symbolItem]);

		// Memoized action button
		const ActionButton = useMemo(() => {
			if (addSymbolLoading) {
				return (
					<ActivityIndicator
						color={palette.headline_primary}
						size={20}
					/>
				);
			}

			if (symbolExists) {
				return <SvgIcon icon={icons.ic_tick_circle} size={24} />;
			}

			return (
				<TouchableOpacity
					activeOpacity={0.5}
					disabled={addSymbolLoading}
					onPress={addSymbolToWatchlist}
				>
					<SvgIcon icon={icons.ic_add_circle} size={24} />
				</TouchableOpacity>
			);
		}, [addSymbolLoading, symbolExists, addSymbolToWatchlist]);

		return (
			<Box
				p='mx'
				flexDirection='row'
				alignItems='center'
				justifyContent='space-between'
				borderBottomWidth={1}
				borderBottomColor='splitter_line'
			>
				<Box flexDirection='row' gap='s'>
					<Box
						height={40}
						width={40}
						borderRadius='mx'
						alignItems='center'
						justifyContent='center'
						style={{ backgroundColor: randomColor }}
					>
						<Text
							color='white'
							fontSize={16}
							fontFamily={AlbertSans.BOLD}
						>
							{symbolName}
						</Text>
					</Box>
					<Box gap='s'>
						<Box flexDirection='row' gap='s'>
							<Text variant='addSymbol' color='headline_primary'>
								{symbolItem?.item?.symbol}
							</Text>
						</Box>
						<Text variant='stockCardCaption'>{shrunkName}</Text>
					</Box>
				</Box>
				<Box flexDirection='row' alignItems='center' gap='s'>
					<Box gap='s'>
						<Text variant='stockCardCaption'>
							{symbolItem?.item?.exchange}
						</Text>
					</Box>
					<Box
						width={30}
						height={30}
						alignItems='center'
						justifyContent='center'
					>
						{ActionButton}
					</Box>
				</Box>
			</Box>
		);
	},
	// Custom comparison function for better memoization
	(prevProps, nextProps) => {
		const prevItem = prevProps.item;
		const nextItem = nextProps.item;

		return (
			prevItem?.item?.symbol === nextItem?.item?.symbol &&
			prevItem?.item?.name === nextItem?.item?.name &&
			prevItem?.item?.exchange === nextItem?.item?.exchange &&
			prevItem?.item?.key === nextItem?.item?.key
		);
	}
);

OptimizedSymbolItem.displayName = "OptimizedSymbolItem";

export default OptimizedSymbolItem;
