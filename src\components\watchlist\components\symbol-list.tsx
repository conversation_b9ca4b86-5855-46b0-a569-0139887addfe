import { Box, Text } from "@/components/restyle";
import { WatchlistPlaceHolder } from "@/components/watchlist/watchlist-placeholder";
import { FlashList } from "@shopify/flash-list";
import { ISearchSymbolResult } from "@/types/response";
import { memo, Suspense, useCallback } from "react";
import OptimizedSymbolItem from "@/components/watchlist/components/optimized-symbol-item";
import { Spacer } from "@/components/spacer";
import { SvgIcon } from "@/components/svg-icon";
import { icons } from "@/assets/icons";

// Memoized Empty Component
const EmptyComponent = memo(() => (
	<Box flex={1} alignItems='center' justifyContent='center'>
		<Spacer size={120} />
		<SvgIcon icon={icons.ic_no_results} size={40} />
		<Spacer size={10} />
		<Text variant='stockCardCaption' color='textInputColor'>
			No Results
		</Text>
	</Box>
));

EmptyComponent.displayName = "EmptyComponent";

const ITEM_HEIGHT = 70;

type SymbolListProps = {
	searchLoading: boolean;
	search: string;
	filteredResults: ISearchSymbolResult[];
	renderListHeader: () => JSX.Element;
};

const SymbolList = memo<SymbolListProps>(
	({ searchLoading, search, filteredResults, renderListHeader }) => {
		const renderSymbolItem = useCallback(
			({ item, index }: { item: ISearchSymbolResult; index: number }) => (
				<Suspense fallback={null}>
					<OptimizedSymbolItem
						item={item}
						index={index}
						target='Cell'
					/>
				</Suspense>
			),
			[]
		);

		const keyExtractor = useCallback(
			(item: ISearchSymbolResult, index: number) =>
				item?.item?.symbol ?? index.toString(),
			[]
		);
		const renderEmpty = useCallback(() => <EmptyComponent />, []);

		return (
			<>
				{searchLoading ? (
					<Box mt='l'>
						<WatchlistPlaceHolder />
					</Box>
				) : (
					<FlashList
						data={search?.trim()?.length ? filteredResults : []}
						renderItem={renderSymbolItem}
						keyExtractor={keyExtractor}
						ListHeaderComponent={renderListHeader}
						ListEmptyComponent={renderEmpty}
						estimatedItemSize={ITEM_HEIGHT}
						showsVerticalScrollIndicator={false}
						getItemType={() => "symbol-item"}
					/>
				)}
			</>
		);
	}
);

SymbolList.displayName = "SymbolList";

export default SymbolList;
