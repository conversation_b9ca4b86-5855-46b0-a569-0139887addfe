import { icons } from "@/assets/icons";
import { Box, Text } from "@/components/restyle";
import { Spacer } from "@/components/spacer";
import { SvgIcon } from "@/components/svg-icon";
import { useAppSelector } from "@/store/hooks";
import { AlbertSans, Theme } from "@/theme/theme";
import { IWatchlistStoreItem } from "@/types";
import { useTheme } from "@shopify/restyle";
import { memo, useCallback } from "react";
import { ActivityIndicator, TouchableOpacity } from "react-native";

interface DeleteModalProps {
	visible: boolean;
	selectedItem: Partial<IWatchlistStoreItem> | null;
	isLoading: boolean;
	onCancel: () => void;
	onConfirm: () => void;
}

const DeleteModal = memo<DeleteModalProps>(
	({ visible, selectedItem, isLoading, onCancel, onConfirm }) => {
		const { colors } = useTheme<Theme>();
		const theme = useAppSelector((state) => state.theme);

		const handleCancel = useCallback(() => {
			if (!isLoading) {
				onCancel();
			}
		}, [isLoading, onCancel]);

		const handleConfirm = useCallback(() => {
			if (!isLoading) {
				onConfirm();
			}
		}, [isLoading, onConfirm]);

		if (!visible) {
			return null;
		}

		return (
			<Box
				position='absolute'
				top={0}
				left={0}
				right={0}
				bottom={0}
				style={{
					backgroundColor:
						theme === "light"
							? "rgba(0, 0, 0, 0.5)"
							: colors.backgroundColor,
					backdropFilter: "blur(10px)",
				}}
				alignItems='center'
				justifyContent='center'
			>
				<Box
					backgroundColor='white'
					borderRadius='m'
					p='l'
					width='80%'
					alignItems='center'
				>
					<SvgIcon icon={icons.ic_trash_red} size={40} />
					<Spacer size={20} />
					<Text variant='addSymbol' textAlign='center'>
						Delete Symbol
					</Text>
					<Spacer size={10} />
					<Text
						variant='basicHeaderText'
						textAlign='center'
						color='caption_text_secondary'
						lineHeight={22}
					>
						Are you sure you want to delete symbol{" "}
						{`"${selectedItem?.ticker?.split(":")[2]}"`}?
					</Text>
					<Spacer size={20} />
					<Box gap='m' width='100%'>
						<TouchableOpacity
							activeOpacity={0.8}
							onPress={handleCancel}
							disabled={isLoading}
						>
							<Box
								backgroundColor='primary_purple'
								borderRadius='s'
								py='m'
								width='100%'
								alignItems='center'
								justifyContent='center'
							>
								<Text
									color='white'
									fontFamily={AlbertSans.BOLD}
								>
									No, Cancel
								</Text>
							</Box>
						</TouchableOpacity>
						<TouchableOpacity
							activeOpacity={0.8}
							onPress={handleConfirm}
							disabled={isLoading}
						>
							<Box
								backgroundColor='card_table_tile_header'
								borderRadius='s'
								py='m'
								width='100%'
								alignItems='center'
								justifyContent='center'
							>
								{isLoading ? (
									<ActivityIndicator
										size='small'
										color='white'
									/>
								) : (
									<Text
										color='headline_primary'
										fontFamily={AlbertSans.BOLD}
									>
										Yes, Delete
									</Text>
								)}
							</Box>
						</TouchableOpacity>
					</Box>
				</Box>
			</Box>
		);
	}
);

DeleteModal.displayName = "DeleteModal";

export default DeleteModal;
