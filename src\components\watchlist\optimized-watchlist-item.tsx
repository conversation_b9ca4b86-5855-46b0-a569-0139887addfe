import { Box, Text } from "@/components/restyle";
import { IWatchlistStoreItem } from "@/types";
import { FC, memo, useCallback, useMemo } from "react";
import { TouchableOpacity, StyleSheet } from "react-native";
import { SvgIcon } from "@/components/svg-icon";
import { formatPrice } from "@/utils/functions";
import { exchangeImageMap } from "@/utils/constants";
import { icons } from "@/assets/icons";
import Svg, { Path } from "react-native-svg";
import ReanimatedSwipeable from "react-native-gesture-handler/ReanimatedSwipeable";
import OptimizedImage from "@/components/ui/optimized-image";
import { useAppSelector } from "@/store/hooks";

type WatchlistItemProps = {
	item: Partial<IWatchlistStoreItem>;
	onDeletePress: () => void;
	onBookmarkPress: () => void;
	index: number;
};

// Memoized right action component
const RightAction = memo(
	({
		onDeletePress,
		onBookmarkPress,
		flag,
		flagColor,
	}: {
		onDeletePress: () => void;
		onBookmarkPress: () => void;
		flag?: boolean;
		flagColor?: string;
	}) => {
		const theme = useAppSelector((state) => state.theme);
		return (
			<Box flexDirection='row' alignSelf='flex-end'>
				<TouchableOpacity activeOpacity={0.5} onPress={onDeletePress}>
					<Box
						height={70}
						width={70}
						backgroundColor='negative'
						alignItems='center'
						justifyContent='center'
					>
						<SvgIcon icon={icons.ic_trash} size={24} />
					</Box>
				</TouchableOpacity>
				<TouchableOpacity activeOpacity={0.5} onPress={onBookmarkPress}>
					<Box
						height={70}
						width={70}
						backgroundColor='headline_primary'
						alignItems='center'
						justifyContent='center'
					>
						<Svg
							width={24}
							height={24}
							viewBox='0 0 24 24'
							// fill={
							// 	flag
							// 		? flagColor
							// 		: theme === "light"
							// 		? "#fff"
							// 		: "#000"
							// }
						>
							<Path
								d='M19 15.273v-3.995c0-3.43 0-5.146-1.025-6.212C16.95 4 15.3 4 12 4S7.05 4 6.025 5.066C5 6.132 5 7.847 5 11.278v3.995c0 2.477 0 3.716.571 4.257.272.258.616.42.982.463.768.09 1.664-.725 3.458-2.356.792-.721 1.189-1.082 1.647-1.177.226-.047.458-.047.684 0 .458.095.855.456 1.647 1.177 1.794 1.63 2.69 2.447 3.458 2.356a1.72 1.72 0 00.982-.463c.571-.541.571-1.78.571-4.257z'
								stroke={
									flag
										? flagColor
										: theme === "light"
										? "#fff"
										: "#000"
								}
								strokeWidth={2}
							/>
						</Svg>
					</Box>
				</TouchableOpacity>
			</Box>
		);
	}
);

RightAction.displayName = "RightAction";

export const OptimizedWatchlistItem: FC<WatchlistItemProps> = memo(
	({ item, onDeletePress, onBookmarkPress, index }) => {
		const {
			symbol,
			full_name,
			change = 0,
			percentChange,
			c,
			exchange,
			flag,
			flagColor,
		} = item ?? {};

		// Memoize calculations
		const isPositive = useMemo(() => (change ?? 0) >= 0, [change]);
		const image = useMemo(() => exchangeImageMap[exchange!], [exchange]);
		const formattedPrice = useMemo(
			() => formatPrice(c!, exchange!),
			[c, exchange]
		);
		const formattedChange = useMemo(() => change?.toFixed(2), [change]);
		const formattedPercentChange = useMemo(
			() => (percentChange ? `(${percentChange?.toFixed(2)}%)` : ""),
			[percentChange]
		);

		// Memoize right action component
		const renderRightActions = useCallback(
			() => (
				<RightAction
					onDeletePress={onDeletePress}
					onBookmarkPress={onBookmarkPress}
					flag={flag}
					flagColor={flagColor}
				/>
			),
			[onDeletePress, onBookmarkPress, flag, flagColor]
		);

		return (
			<ReanimatedSwipeable
				containerStyle={styles.swipeable}
				friction={2}
				enableTrackpadTwoFingerGesture
				rightThreshold={40}
				renderRightActions={renderRightActions}
			>
				<Box
					height={70}
					alignItems='center'
					flexDirection='row'
					justifyContent='space-between'
					width='100%'
					px='mx'
					borderBottomWidth={1}
					borderBottomColor='splitter_line'
					backgroundColor='backgroundColor'
				>
					<Box gap='s' flexDirection='row' flex={1}>
						<Box
							width={37}
							height={37}
							borderRadius='mx'
							alignItems='center'
							justifyContent='center'
							borderWidth={1}
							borderColor='borderGray'
							overflow='hidden'
						>
							{typeof image === "function" ? (
								<SvgIcon icon={image} size={20} />
							) : (
								<OptimizedImage
									source={image}
									width={37}
									height={37}
									borderRadius='m'
									lazy={index > 10} // Only lazy load items beyond the initial viewport
								/>
							)}
						</Box>
						<Box gap='xs' flex={1} pr='s'>
							<Text variant='stockCardName' color='stockCardName'>
								{symbol}
							</Text>
							<Text
								variant='stockCardCaption'
								numberOfLines={1}
								ellipsizeMode='clip'
							>
								{full_name}
							</Text>
						</Box>
					</Box>
					<Box alignItems='flex-end' justifyContent='center' gap='xs'>
						<Text variant='stockCardName' color='stockCardName'>
							{formattedPrice}
						</Text>
						<Box flexDirection='row' gap='s'>
							<Text
								variant='stockCardCaption'
								color={
									isPositive ? "primary_green" : "negative"
								}
							>
								{isPositive ? "+" : ""}
								{formattedChange}
							</Text>
							<Text
								variant='stockCardCaption'
								color={
									isPositive ? "primary_green" : "negative"
								}
							>
								{formattedPercentChange}
							</Text>
						</Box>
					</Box>
				</Box>
			</ReanimatedSwipeable>
		);
	},
	// Custom comparison function for better memoization
	(prevProps, nextProps) => {
		const prevItem = prevProps.item;
		const nextItem = nextProps.item;

		return (
			prevItem?.symbol === nextItem?.symbol &&
			prevItem?.c === nextItem?.c &&
			prevItem?.change === nextItem?.change &&
			prevItem?.percentChange === nextItem?.percentChange &&
			prevItem?.flag === nextItem?.flag &&
			prevItem?.flagColor === nextItem?.flagColor &&
			prevProps.index === nextProps.index
		);
	}
);

OptimizedWatchlistItem.displayName = "OptimizedWatchlistItem";

const styles = StyleSheet.create({
	swipeable: {
		overflow: "hidden",
	},
});

export default OptimizedWatchlistItem;
