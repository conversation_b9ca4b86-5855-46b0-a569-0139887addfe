import { icons } from "@/assets/icons";
import { Box, Text } from "@/components/restyle";
import { SvgIcon } from "@/components/svg-icon";
import { OptimizedWatchlistItem } from "@/components/watchlist/optimized-watchlist-item";
import DeleteModal from "@/components/watchlist/delete-modal";
import useWatchlist from "@/hooks/useWatchlist";
import { showToast } from "@/services/toast";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { removeWatchlistItem } from "@/store/reducers/watchlist.reducer";
import {
	useAddSymbolMutation,
	useRemoveSymbolMutation,
} from "@/store/services/watchlist-api";
import { IWatchlistStoreItem } from "@/types";
import { ISettingsDataType } from "@/types/request";
import { PortalConstants } from "@/utils/constants";
import { sortWatchlistItemsArray } from "@/utils/functions";
import { Portal } from "@gorhom/portal";
import { router } from "expo-router";
import { memo, useCallback, useMemo, useState } from "react";
import { TouchableOpacity } from "react-native";
import { FlashList } from "@shopify/flash-list";

const ITEM_HEIGHT = 70;

const EmptyWatchlist = memo(() => {
	return (
		<Box px='mx'>
			<Box
				p='l'
				gap='m'
				borderRadius='m'
				borderWidth={1.5}
				borderStyle='dashed'
				borderColor='outline'
				alignItems='center'
				justifyContent='center'
			>
				<SvgIcon icon={icons.ic_qoute} size={48} />
				<Text variant='stockCardCaption' fontSize={16} lineHeight={16}>
					There are no symbols.
				</Text>
				<TouchableOpacity
					hitSlop={10}
					activeOpacity={0.5}
					onPress={() => {
						router.push({
							pathname: "/add-symbol",
						});
					}}
				>
					<Text variant='addSymbol'>+ Add Symbol</Text>
				</TouchableOpacity>
			</Box>
		</Box>
	);
});

EmptyWatchlist.displayName = "EmptyWatchlist";

const OptimizedWatchlistMain = () => {
	const dispatch = useAppDispatch();
	const watchlistState = useAppSelector((state) => state.watchlist);

	// Initialize the watchlist hook
	useWatchlist();

	const [addSymbol] = useAddSymbolMutation();
	const [removeSymbol, { isLoading: removeSymbolLoading }] =
		useRemoveSymbolMutation();

	// State for delete modal
	const [selectedItem, setSelectedItem] =
		useState<Partial<IWatchlistStoreItem> | null>(null);
	const [deleteModalVisible, setDeleteModalVisible] =
		useState<boolean>(false);

	// Handle removing a symbol from the watchlist
	const removeSymbolsFromWatchlist = useCallback(async () => {
		try {
			if (!selectedItem) {
				return;
			}

			const res = await removeSymbol({
				action: "WATCHLIST_REMOVE",
				data_type: ISettingsDataType.WATCHLIST,
				key: watchlistState?.key!,
				name: watchlistState?.name,
				value: {
					settings: {
						savedToCloud: true,
					},
					objects: [
						{
							source: selectedItem?.source!,
							ticker: selectedItem?.ticker!,
						},
					],
				},
			}).unwrap();

			if (res.status === 200) {
				dispatch(removeWatchlistItem(selectedItem?.ticker!));
				showToast(
					`Symbol ${
						selectedItem?.ticker?.split(":")[2]
					} removed from watchlist!`
				);
			}
		} catch (error) {
			showToast("Failed to remove symbol. Please try again.");
		}
	}, [selectedItem, watchlistState, removeSymbol, dispatch]);

	// Memoize watchlist items with sorting
	const watchlistItems = useMemo(() => {
		const items = watchlistState?.items ?? [];
		if (items?.length) {
			if (watchlistState?.sortBy) {
				// Create a copy of the array before sorting to avoid mutating Redux state
				return [...items]
					.sort(sortWatchlistItemsArray(watchlistState?.sortBy))
					.map((w, index) => ({
						...w,
						id: w.ticker ?? "",
						index,
					}));
			}

			return items.map((w, index) => ({
				...w,
				id: w.ticker ?? "",
				index,
			}));
		}

		return [];
	}, [watchlistState]);

	// Handle bookmarking a symbol
	const bookmarkSymbol = useCallback(
		async (symbol: Partial<IWatchlistStoreItem>) => {
			if (!symbol?.ticker) return;

			try {
				const name = watchlistState?.name;

				const res = await addSymbol({
					action: "WATCHLIST_ADD",
					data_type: ISettingsDataType.WATCHLIST,
					key: watchlistState?.key,
					name,
					value: {
						settings: {
							savedToCloud: true,
						},
						objects: [
							{
								exchange: symbol?.exchange!,
								segment: symbol?.segment!,
								source: symbol?.source!,
								symbol: symbol?.symbol!,
								ticker: symbol?.ticker!,
								flag: true,
								flagColor: "red",
							},
						],
					},
				}).unwrap();

				if (res.status === 200) {
					showToast(`Symbol ${symbol?.symbol} added to bookmark!`);
				}
			} catch (error) {
				showToast("Failed to bookmark symbol. Please try again.");
			}
		},
		[watchlistState, addSymbol]
	);

	// Memoize handlers for item interactions
	const handleDeletePress = useCallback(
		(item: Partial<IWatchlistStoreItem>) => {
			setSelectedItem(item);
			setDeleteModalVisible(true);
		},
		[]
	);

	const handleBookmarkPress = useCallback(
		(item: Partial<IWatchlistStoreItem>) => {
			bookmarkSymbol(item);
		},
		[bookmarkSymbol]
	);

	// Optimized render function with index for better performance
	const renderWatchlistItem = useCallback(
		({
			item,
			index,
		}: {
			item: Partial<IWatchlistStoreItem>;
			index: number;
		}) => (
			<OptimizedWatchlistItem
				item={item}
				index={index}
				onDeletePress={() => handleDeletePress(item)}
				onBookmarkPress={() => handleBookmarkPress(item)}
			/>
		),
		[handleDeletePress, handleBookmarkPress]
	);

	const keyExtractor = useCallback(
		(item: Partial<IWatchlistStoreItem>, idx: number) =>
			item?.ticker! ?? idx.toString(),
		[]
	);

	const renderEmpty = useCallback(() => <EmptyWatchlist />, []);

	// Handle modal actions
	const handleModalCancel = useCallback(() => {
		setDeleteModalVisible(false);
		setSelectedItem(null);
	}, []);

	const handleModalConfirm = useCallback(async () => {
		await removeSymbolsFromWatchlist();
		setDeleteModalVisible(false);
		setSelectedItem(null);
	}, [removeSymbolsFromWatchlist]);

	return (
		<>
			<FlashList
				data={watchlistItems}
				renderItem={renderWatchlistItem}
				keyExtractor={keyExtractor}
				showsVerticalScrollIndicator={false}
				estimatedItemSize={ITEM_HEIGHT}
				ListEmptyComponent={renderEmpty}
				removeClippedSubviews
				scrollEventThrottle={16}
				getItemType={() => "watchlist-item"}
			/>

			<Portal hostName={PortalConstants.WATCHLIST_MAIN}>
				<DeleteModal
					visible={deleteModalVisible}
					selectedItem={selectedItem}
					isLoading={removeSymbolLoading}
					onCancel={handleModalCancel}
					onConfirm={handleModalConfirm}
				/>
			</Portal>
		</>
	);
};

OptimizedWatchlistMain.displayName = "OptimizedWatchlistMain";

export default OptimizedWatchlistMain;
