import { useCallback, useMemo, useRef } from 'react';
import { InteractionManager } from 'react-native';
import { toolbarPerformanceTracker } from '@/utils/toolbarPerformanceTest';

interface UseOptimizedToolbarProps {
  onInterval: () => void;
  onIndicators: () => void;
  onDrawings: () => void;
  onTrade: () => void;
  onMore: () => void;
  enablePerformanceTracking?: boolean;
}

/**
 * Hook for optimized toolbar interactions with performance tracking
 */
export const useOptimizedToolbar = ({
  onInterval,
  onIndicators,
  onDrawings,
  onTrade,
  onMore,
  enablePerformanceTracking = __DEV__,
}: UseOptimizedToolbarProps) => {
  const interactionRefs = useRef<Map<string, any>>(new Map());

  // Create optimized handlers with performance tracking
  const createOptimizedHandler = useCallback((
    buttonName: string,
    originalHandler: () => void
  ) => {
    return () => {
      const startTime = Date.now();
      
      // Start performance tracking
      if (enablePerformanceTracking) {
        toolbarPerformanceTracker.startButtonClick(buttonName);
      }

      // Use InteractionManager for smooth interactions
      const interaction = InteractionManager.runAfterInteractions(() => {
        try {
          // Execute the original handler
          originalHandler();
          
          // End click tracking
          if (enablePerformanceTracking) {
            const clickTime = Date.now() - startTime;
            toolbarPerformanceTracker.endButtonClick(buttonName);
            
            // Set up modal tracking (will be completed when modal actually opens)
            setTimeout(() => {
              if (enablePerformanceTracking) {
                toolbarPerformanceTracker.endModalOpen(buttonName);
              }
            }, 100); // Assume modal opens within 100ms
          }
        } catch (error) {
          console.error(`Error in ${buttonName} handler:`, error);
        }
      });

      // Store interaction reference for cleanup
      interactionRefs.current.set(buttonName, interaction);
    };
  }, [enablePerformanceTracking]);

  // Memoized optimized handlers
  const optimizedHandlers = useMemo(() => ({
    handleInterval: createOptimizedHandler('interval', onInterval),
    handleIndicators: createOptimizedHandler('indicators', onIndicators),
    handleDrawings: createOptimizedHandler('drawings', onDrawings),
    handleTrade: createOptimizedHandler('trade', onTrade),
    handleMore: createOptimizedHandler('more', onMore),
  }), [
    createOptimizedHandler,
    onInterval,
    onIndicators,
    onDrawings,
    onTrade,
    onMore,
  ]);

  // Cleanup function
  const cleanup = useCallback(() => {
    interactionRefs.current.forEach((interaction) => {
      if (interaction && typeof interaction.cancel === 'function') {
        interaction.cancel();
      }
    });
    interactionRefs.current.clear();
  }, []);

  // Performance metrics getter
  const getPerformanceMetrics = useCallback(() => {
    if (!enablePerformanceTracking) return null;
    
    return {
      allMetrics: toolbarPerformanceTracker.getMetrics(),
      averageMetrics: toolbarPerformanceTracker.getAverageMetrics(),
      report: toolbarPerformanceTracker.generateToolbarReport(),
      status: toolbarPerformanceTracker.getBenchmarkStatus(),
      insights: toolbarPerformanceTracker.getPerformanceInsights(),
    };
  }, [enablePerformanceTracking]);

  return {
    ...optimizedHandlers,
    cleanup,
    getPerformanceMetrics,
  };
};

/**
 * Hook for toolbar button optimization with haptic feedback and smooth animations
 */
export const useOptimizedButton = (
  buttonName: string,
  onPress: () => void,
  options: {
    hapticFeedback?: boolean;
    enableTracking?: boolean;
    debounceMs?: number;
  } = {}
) => {
  const {
    hapticFeedback = true,
    enableTracking = __DEV__,
    debounceMs = 0,
  } = options;

  const lastPressTime = useRef(0);
  const pressStartTime = useRef(0);

  const optimizedPress = useCallback(() => {
    const now = Date.now();
    
    // Debounce if specified
    if (debounceMs > 0 && now - lastPressTime.current < debounceMs) {
      return;
    }
    
    lastPressTime.current = now;
    pressStartTime.current = now;

    // Start performance tracking
    if (enableTracking) {
      toolbarPerformanceTracker.startButtonClick(buttonName);
    }

    // Add haptic feedback
    if (hapticFeedback) {
      try {
        // Use native haptic feedback if available
        if (typeof navigator !== 'undefined' && 'vibrate' in navigator) {
          navigator.vibrate(10);
        }
      } catch (error) {
        // Haptic feedback not available
      }
    }

    // Use requestAnimationFrame for smooth interaction
    requestAnimationFrame(() => {
      try {
        onPress();
        
        // End performance tracking
        if (enableTracking) {
          const clickTime = Date.now() - pressStartTime.current;
          toolbarPerformanceTracker.endButtonClick(buttonName);
        }
      } catch (error) {
        console.error(`Error in ${buttonName} button press:`, error);
      }
    });
  }, [buttonName, onPress, hapticFeedback, enableTracking, debounceMs]);

  return {
    onPress: optimizedPress,
    isDebounced: debounceMs > 0,
  };
};

/**
 * Hook for modal performance tracking
 */
export const useModalPerformanceTracking = (modalName: string) => {
  const openStartTime = useRef(0);

  const startModalOpen = useCallback(() => {
    openStartTime.current = Date.now();
    if (__DEV__) {
      console.log(`[Modal Performance] ${modalName} opening started`);
    }
  }, [modalName]);

  const endModalOpen = useCallback(() => {
    if (openStartTime.current === 0) return;
    
    const openTime = Date.now() - openStartTime.current;
    openStartTime.current = 0;
    
    // Update toolbar performance tracker
    toolbarPerformanceTracker.endModalOpen(modalName);
    
    if (__DEV__) {
      console.log(`[Modal Performance] ${modalName} opened in ${openTime}ms`);
    }
    
    return openTime;
  }, [modalName]);

  return {
    startModalOpen,
    endModalOpen,
  };
};
