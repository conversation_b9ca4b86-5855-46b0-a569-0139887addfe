import { useEffect, useRef, useState, useCallback } from 'react';
import { InteractionManager } from 'react-native';

interface TabOptimizationOptions {
  preloadDelay?: number;
  enablePreloading?: boolean;
}

/**
 * Hook to optimize tab switching performance
 * - Defers heavy operations until after tab transition
 * - Preloads adjacent tabs
 * - Manages component mounting states
 */
export const useTabOptimization = (
  tabName: string,
  options: TabOptimizationOptions = {}
) => {
  const { preloadDelay = 100, enablePreloading = true } = options;
  const [isReady, setIsReady] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const interactionHandle = useRef<any>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Defer rendering until after interactions complete
  useEffect(() => {
    const task = InteractionManager.runAfterInteractions(() => {
      setIsReady(true);
      // Small delay to ensure smooth transition
      timeoutRef.current = setTimeout(() => {
        setShouldRender(true);
      }, preloadDelay);
    });

    interactionHandle.current = task;

    return () => {
      if (interactionHandle.current) {
        interactionHandle.current.cancel();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [preloadDelay]);

  // Force immediate rendering (for active tab)
  const forceRender = useCallback(() => {
    setIsReady(true);
    setShouldRender(true);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  // Reset states when tab changes
  const reset = useCallback(() => {
    setIsReady(false);
    setShouldRender(false);
  }, []);

  return {
    isReady,
    shouldRender,
    forceRender,
    reset,
  };
};

/**
 * Hook to manage tab preloading
 */
export const useTabPreloader = (activeTab: string, allTabs: string[]) => {
  const [preloadedTabs, setPreloadedTabs] = useState<Set<string>>(new Set());

  useEffect(() => {
    const currentIndex = allTabs.indexOf(activeTab);
    if (currentIndex === -1) return;

    // Preload adjacent tabs
    const tabsToPreload = new Set<string>();
    
    // Add current tab
    tabsToPreload.add(activeTab);
    
    // Add previous tab
    if (currentIndex > 0) {
      tabsToPreload.add(allTabs[currentIndex - 1]);
    }
    
    // Add next tab
    if (currentIndex < allTabs.length - 1) {
      tabsToPreload.add(allTabs[currentIndex + 1]);
    }

    setPreloadedTabs(tabsToPreload);
  }, [activeTab, allTabs]);

  const shouldPreload = useCallback((tabName: string) => {
    return preloadedTabs.has(tabName);
  }, [preloadedTabs]);

  return { shouldPreload, preloadedTabs };
};

/**
 * Hook to optimize component re-renders
 */
export const useRenderOptimization = () => {
  const renderCountRef = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCountRef.current += 1;
    lastRenderTime.current = Date.now();
  });

  const getRenderStats = useCallback(() => ({
    renderCount: renderCountRef.current,
    lastRenderTime: lastRenderTime.current,
  }), []);

  return { getRenderStats };
};
