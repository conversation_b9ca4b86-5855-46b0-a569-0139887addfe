import { useState, useCallback } from 'react';

export interface TradeFormValues {
  side: "buy" | "sell";
  orderType: "market" | "limit" | "stop" | "stoplimit";
  price: string;
  stopPrice: string;
  stopLimit: string;
  stopLimitEnabled: boolean;
  size: string;
  takeProfit: string;
  stopLoss: string;
  icebergLegs: string;
  takeProfitEnabled: boolean;
  stopLossEnabled: boolean;
  [key: string]: any; // Allow additional properties for custom forms
}

export interface FormErrors {
  [key: string]: string;
}

export interface UseTradeFormResult {
  values: TradeFormValues;
  errors: FormErrors;
  touched: Record<string, boolean>;
  handleChange: (name: keyof TradeFormValues, value: any) => void;
  handleBlur: (name: keyof TradeFormValues) => void;
  setValues: (newValues: Partial<TradeFormValues>) => void;
  resetForm: () => void;
  validateForm: () => FormErrors;
}

export const useTradeForm = (
  initialValues: TradeFormValues,
  validate: (values: TradeFormValues) => FormErrors
): UseTradeFormResult => {
  const [values, setValues] = useState<TradeFormValues>(initialValues);
  const [errors, setErrors] = useState<FormErrors>(validate(initialValues));
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const handleChange = useCallback((name: keyof TradeFormValues, value: any) => {
    const newValues = { ...values, [name]: value };
    setValues(newValues);
    setErrors(validate(newValues));
  }, [values, validate]);

  const handleBlur = useCallback((name: keyof TradeFormValues) => {
    setTouched(prev => ({ ...prev, [name]: true }));
  }, []);

  const setFormValues = useCallback((newValues: Partial<TradeFormValues>) => {
    const updatedValues = { ...values, ...newValues };
    setValues(updatedValues);
    setErrors(validate(updatedValues));
  }, [values, validate]);

  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors(validate(initialValues));
    setTouched({});
  }, [initialValues, validate]);

  const validateForm = useCallback(() => {
    const validationErrors = validate(values);
    setErrors(validationErrors);
    return validationErrors;
  }, [values, validate]);

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setValues: setFormValues,
    resetForm,
    validateForm
  };
};
