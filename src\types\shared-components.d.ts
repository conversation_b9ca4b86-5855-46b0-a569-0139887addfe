declare module '@gocharting/shared-components/src/hooks/useForm2' {
  export function useForm<T>(
    initialValues: T,
    params: {
      validate?: (values: T) => Record<string, string>;
      validationIsAsync?: boolean;
      onChange?: (name: string, values: T, ...rest: any[]) => T;
      onBlur?: (...args: any[]) => void;
      onPreSubmit?: (...args: any[]) => void;
      onSubmit?: (values: T, ...args: any[]) => Promise<any>;
    }
  ): {
    form: {
      values: T;
      errors?: Record<string, string>;
      touched?: Record<string, boolean>;
      validationInProgress?: boolean;
      showErrors?: boolean;
      submitInProgress?: boolean;
    };
    handleChange: (name: string, ...rest: any[]) => void;
    handleBlur: (name: string, ...rest: any[]) => void;
    setFormValues: (values: T | ((prevValues: T) => T)) => void;
    handleSubmit: (...rest: any[]) => void;
  };
}
