/**
 * Charts Tab Performance Testing Utilities
 * Specialized testing for charts screen optimizations
 */

import { performanceTracker } from './performanceTest';

interface ChartsPerformanceMetrics {
  componentName: string;
  loadTime: number;
  renderTime: number;
  modalLoadTime: number;
  interactionTime: number;
  memoryUsage?: number;
  timestamp: number;
}

class ChartsPerformanceTracker {
  private metrics: ChartsPerformanceMetrics[] = [];
  private startTimes: Map<string, number> = new Map();

  startTracking(componentName: string, type: 'load' | 'render' | 'modal' | 'interaction') {
    const key = `${componentName}-${type}`;
    this.startTimes.set(key, Date.now());
    
    if (__DEV__) {
      console.log(`[Charts Performance] Starting ${type} tracking for ${componentName}`);
    }
  }

  endTracking(componentName: string, type: 'load' | 'render' | 'modal' | 'interaction') {
    const key = `${componentName}-${type}`;
    const startTime = this.startTimes.get(key);
    
    if (!startTime) return 0;
    
    const duration = Date.now() - startTime;
    this.updateMetrics(componentName, type, duration);
    this.startTimes.delete(key);
    
    if (__DEV__) {
      console.log(`[Charts Performance] ${componentName} ${type}: ${duration}ms`);
    }
    
    return duration;
  }

  private updateMetrics(componentName: string, type: string, duration: number) {
    let metric = this.metrics.find(m => 
      m.componentName === componentName && 
      Math.abs(m.timestamp - Date.now()) < 10000 // Within 10 seconds
    );

    if (!metric) {
      metric = {
        componentName,
        loadTime: 0,
        renderTime: 0,
        modalLoadTime: 0,
        interactionTime: 0,
        timestamp: Date.now(),
      };
      this.metrics.push(metric);
    }

    switch (type) {
      case 'load':
        metric.loadTime = duration;
        break;
      case 'render':
        metric.renderTime = duration;
        break;
      case 'modal':
        metric.modalLoadTime = duration;
        break;
      case 'interaction':
        metric.interactionTime = duration;
        break;
    }
  }

  getMetrics(componentName?: string): ChartsPerformanceMetrics[] {
    if (componentName) {
      return this.metrics.filter(m => m.componentName === componentName);
    }
    return [...this.metrics];
  }

  getAverageMetrics(componentName?: string) {
    const metrics = this.getMetrics(componentName);
    if (metrics.length === 0) return null;

    const totals = metrics.reduce((acc, m) => ({
      loadTime: acc.loadTime + m.loadTime,
      renderTime: acc.renderTime + m.renderTime,
      modalLoadTime: acc.modalLoadTime + m.modalLoadTime,
      interactionTime: acc.interactionTime + m.interactionTime,
    }), { loadTime: 0, renderTime: 0, modalLoadTime: 0, interactionTime: 0 });

    const count = metrics.length;
    return {
      componentName: componentName || 'all',
      avgLoadTime: Math.round(totals.loadTime / count),
      avgRenderTime: Math.round(totals.renderTime / count),
      avgModalLoadTime: Math.round(totals.modalLoadTime / count),
      avgInteractionTime: Math.round(totals.interactionTime / count),
      sampleCount: count,
    };
  }

  generateChartsReport(): string {
    const components = ['ChartsScreen', 'Chart', 'ChartHeader', 'ChartToolbar', 'Legends'];
    let report = '\n=== Charts Performance Report ===\n';
    report += 'Component | Load Time | Render Time | Modal Time | Interaction Time | Samples\n';
    report += '----------|-----------|-------------|------------|------------------|--------\n';

    components.forEach(component => {
      const avg = this.getAverageMetrics(component);
      if (avg) {
        report += `${component.padEnd(9)} | ${avg.avgLoadTime.toString().padEnd(9)} | ${avg.avgRenderTime.toString().padEnd(11)} | ${avg.avgModalLoadTime.toString().padEnd(10)} | ${avg.avgInteractionTime.toString().padEnd(16)} | ${avg.sampleCount}\n`;
      }
    });

    const overall = this.getAverageMetrics();
    if (overall) {
      report += '----------|-----------|-------------|------------|------------------|--------\n';
      report += `Overall   | ${overall.avgLoadTime.toString().padEnd(9)} | ${overall.avgRenderTime.toString().padEnd(11)} | ${overall.avgModalLoadTime.toString().padEnd(10)} | ${overall.avgInteractionTime.toString().padEnd(16)} | ${overall.sampleCount}\n`;
    }

    return report;
  }

  getBenchmarkStatus(): string {
    const overall = this.getAverageMetrics();
    if (!overall) return 'No data available';

    const { avgLoadTime, avgRenderTime, avgModalLoadTime, avgInteractionTime } = overall;
    
    // Performance thresholds for charts (in ms)
    const EXCELLENT_LOAD = 100;
    const GOOD_LOAD = 300;
    const EXCELLENT_RENDER = 50;
    const GOOD_RENDER = 150;
    const EXCELLENT_MODAL = 200;
    const GOOD_MODAL = 500;
    const EXCELLENT_INTERACTION = 100;
    const GOOD_INTERACTION = 300;

    let status = '';
    
    // Load time status
    if (avgLoadTime <= EXCELLENT_LOAD) {
      status += '🚀 Excellent load performance';
    } else if (avgLoadTime <= GOOD_LOAD) {
      status += '✅ Good load performance';
    } else {
      status += '⚠️ Load performance needs improvement';
    }

    status += ' | ';

    // Render time status
    if (avgRenderTime <= EXCELLENT_RENDER) {
      status += '🚀 Excellent render performance';
    } else if (avgRenderTime <= GOOD_RENDER) {
      status += '✅ Good render performance';
    } else {
      status += '⚠️ Render performance needs improvement';
    }

    status += ' | ';

    // Modal time status
    if (avgModalLoadTime <= EXCELLENT_MODAL) {
      status += '🚀 Excellent modal performance';
    } else if (avgModalLoadTime <= GOOD_MODAL) {
      status += '✅ Good modal performance';
    } else {
      status += '⚠️ Modal performance needs improvement';
    }

    return status;
  }

  clearMetrics() {
    this.metrics = [];
    this.startTimes.clear();
  }
}

// Global charts performance tracker
export const chartsPerformanceTracker = new ChartsPerformanceTracker();

/**
 * Hook for measuring charts component performance
 */
export const useChartsPerformanceTracking = (componentName: string) => {
  const startLoadTracking = () => {
    chartsPerformanceTracker.startTracking(componentName, 'load');
  };

  const endLoadTracking = () => {
    return chartsPerformanceTracker.endTracking(componentName, 'load');
  };

  const startRenderTracking = () => {
    chartsPerformanceTracker.startTracking(componentName, 'render');
  };

  const endRenderTracking = () => {
    return chartsPerformanceTracker.endTracking(componentName, 'render');
  };

  const startModalTracking = () => {
    chartsPerformanceTracker.startTracking(componentName, 'modal');
  };

  const endModalTracking = () => {
    return chartsPerformanceTracker.endTracking(componentName, 'modal');
  };

  const startInteractionTracking = () => {
    chartsPerformanceTracker.startTracking(componentName, 'interaction');
  };

  const endInteractionTracking = () => {
    return chartsPerformanceTracker.endTracking(componentName, 'interaction');
  };

  return {
    startLoadTracking,
    endLoadTracking,
    startRenderTracking,
    endRenderTracking,
    startModalTracking,
    endModalTracking,
    startInteractionTracking,
    endInteractionTracking,
  };
};

/**
 * Test runner for charts performance
 */
export const runChartsPerformanceTest = async (iterations: number = 3) => {
  console.log(`[Charts Performance Test] Starting test with ${iterations} iterations`);
  
  chartsPerformanceTracker.clearMetrics();
  
  for (let i = 0; i < iterations; i++) {
    console.log(`[Charts Performance Test] Iteration ${i + 1}/${iterations}`);
    
    // Simulate charts screen load
    chartsPerformanceTracker.startTracking('ChartsScreen', 'load');
    await new Promise(resolve => setTimeout(resolve, 50));
    chartsPerformanceTracker.endTracking('ChartsScreen', 'load');
    
    // Simulate modal interactions
    const modals = ['interval', 'indicators', 'drawings', 'trade'];
    for (const modal of modals) {
      chartsPerformanceTracker.startTracking(modal, 'modal');
      await new Promise(resolve => setTimeout(resolve, 30));
      chartsPerformanceTracker.endTracking(modal, 'modal');
    }
    
    // Wait between iterations
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  const report = chartsPerformanceTracker.generateChartsReport();
  const status = chartsPerformanceTracker.getBenchmarkStatus();
  
  console.log(report);
  console.log(`\n${status}\n`);
  
  return {
    report,
    status,
    metrics: chartsPerformanceTracker.getMetrics(),
  };
};
