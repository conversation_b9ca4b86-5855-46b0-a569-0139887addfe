/**
 * Performance testing utilities for tab switching optimization
 */

interface PerformanceMetrics {
  componentName: string;
  mountTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

class PerformanceTracker {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private startTimes: Map<string, number> = new Map();

  startTracking(componentName: string) {
    this.startTimes.set(componentName, Date.now());
  }

  endTracking(componentName: string, type: 'mount' | 'render' | 'interaction') {
    const startTime = this.startTimes.get(componentName);
    if (!startTime) return;

    const endTime = Date.now();
    const duration = endTime - startTime;

    const existing = this.metrics.get(componentName) || {
      componentName,
      mountTime: 0,
      renderTime: 0,
      interactionTime: 0,
    };

    switch (type) {
      case 'mount':
        existing.mountTime = duration;
        break;
      case 'render':
        existing.renderTime = duration;
        break;
      case 'interaction':
        existing.interactionTime = duration;
        break;
    }

    this.metrics.set(componentName, existing);
    this.startTimes.delete(componentName);

    if (__DEV__) {
      console.log(`[Performance] ${componentName} ${type}: ${duration}ms`);
    }
  }

  getMetrics(componentName?: string): PerformanceMetrics | PerformanceMetrics[] {
    if (componentName) {
      return this.metrics.get(componentName) || {
        componentName,
        mountTime: 0,
        renderTime: 0,
        interactionTime: 0,
      };
    }
    return Array.from(this.metrics.values());
  }

  clearMetrics() {
    this.metrics.clear();
    this.startTimes.clear();
  }

  generateReport(): string {
    const allMetrics = this.getMetrics() as PerformanceMetrics[];
    
    let report = '\n=== Performance Report ===\n';
    report += 'Component Name | Mount Time | Render Time | Interaction Time\n';
    report += '---------------|------------|-------------|------------------\n';
    
    allMetrics.forEach(metric => {
      report += `${metric.componentName.padEnd(14)} | ${metric.mountTime.toString().padEnd(10)} | ${metric.renderTime.toString().padEnd(11)} | ${metric.interactionTime}\n`;
    });
    
    return report;
  }
}

// Global performance tracker instance
export const performanceTracker = new PerformanceTracker();

/**
 * Hook for easy performance tracking in components
 */
export const usePerformanceTracking = (componentName: string) => {
  const startTracking = (type: 'mount' | 'render' | 'interaction') => {
    performanceTracker.startTracking(`${componentName}-${type}`);
  };

  const endTracking = (type: 'mount' | 'render' | 'interaction') => {
    performanceTracker.endTracking(`${componentName}-${type}`, type);
  };

  return { startTracking, endTracking };
};

/**
 * Measure tab switch performance
 */
export const measureTabSwitch = (fromTab: string, toTab: string) => {
  const startTime = Date.now();
  
  return {
    complete: () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (__DEV__) {
        console.log(`[Tab Switch] ${fromTab} → ${toTab}: ${duration}ms`);
      }
      
      return duration;
    }
  };
};

/**
 * Memory usage tracking (if available)
 */
export const trackMemoryUsage = (componentName: string) => {
  if (typeof performance !== 'undefined' && 'memory' in performance) {
    const memory = (performance as any).memory;
    const usage = {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
    };
    
    if (__DEV__) {
      console.log(`[Memory] ${componentName}:`, usage);
    }
    
    return usage;
  }
  
  return null;
};
