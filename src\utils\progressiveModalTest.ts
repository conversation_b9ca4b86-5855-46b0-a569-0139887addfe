/**
 * Progressive Modal Loading Performance Test
 * Tests the new instant modal opening with lazy content loading
 */

interface ProgressiveModalMetrics {
  modalName: string;
  modalOpenTime: number;
  contentLoadTime: number;
  totalTime: number;
  skeletonDisplayTime: number;
  userPerceiveTime: number; // Time until user sees something
  timestamp: number;
}

class ProgressiveModalTracker {
  private metrics: ProgressiveModalMetrics[] = [];
  private modalStartTimes: Map<string, number> = new Map();
  private contentStartTimes: Map<string, number> = new Map();

  startModalOpen(modalName: string) {
    this.modalStartTimes.set(modalName, Date.now());
    
    if (__DEV__) {
      console.log(`[Progressive Modal] ${modalName} modal opening started`);
    }
  }

  modalVisible(modalName: string) {
    const startTime = this.modalStartTimes.get(modalName);
    if (!startTime) return 0;
    
    const modalOpenTime = Date.now() - startTime;
    this.contentStartTimes.set(modalName, Date.now());
    
    if (__DEV__) {
      console.log(`[Progressive Modal] ${modalName} modal visible: ${modalOpenTime}ms`);
    }
    
    return modalOpenTime;
  }

  contentLoaded(modalName: string) {
    const modalStartTime = this.modalStartTimes.get(modalName);
    const contentStartTime = this.contentStartTimes.get(modalName);
    
    if (!modalStartTime || !contentStartTime) return 0;
    
    const now = Date.now();
    const modalOpenTime = contentStartTime - modalStartTime;
    const contentLoadTime = now - contentStartTime;
    const totalTime = now - modalStartTime;
    const skeletonDisplayTime = contentLoadTime;
    const userPerceiveTime = modalOpenTime; // User sees modal immediately
    
    const metric: ProgressiveModalMetrics = {
      modalName,
      modalOpenTime,
      contentLoadTime,
      totalTime,
      skeletonDisplayTime,
      userPerceiveTime,
      timestamp: now,
    };
    
    this.metrics.push(metric);
    
    // Cleanup
    this.modalStartTimes.delete(modalName);
    this.contentStartTimes.delete(modalName);
    
    if (__DEV__) {
      console.log(`[Progressive Modal] ${modalName} complete - Modal: ${modalOpenTime}ms, Content: ${contentLoadTime}ms, Total: ${totalTime}ms`);
    }
    
    return contentLoadTime;
  }

  getMetrics(modalName?: string): ProgressiveModalMetrics[] {
    if (modalName) {
      return this.metrics.filter(m => m.modalName === modalName);
    }
    return [...this.metrics];
  }

  getAverageMetrics(modalName?: string) {
    const metrics = this.getMetrics(modalName);
    if (metrics.length === 0) return null;

    const totals = metrics.reduce((acc, m) => ({
      modalOpenTime: acc.modalOpenTime + m.modalOpenTime,
      contentLoadTime: acc.contentLoadTime + m.contentLoadTime,
      totalTime: acc.totalTime + m.totalTime,
      userPerceiveTime: acc.userPerceiveTime + m.userPerceiveTime,
    }), { modalOpenTime: 0, contentLoadTime: 0, totalTime: 0, userPerceiveTime: 0 });

    const count = metrics.length;
    return {
      modalName: modalName || 'all',
      avgModalOpenTime: Math.round(totals.modalOpenTime / count),
      avgContentLoadTime: Math.round(totals.contentLoadTime / count),
      avgTotalTime: Math.round(totals.totalTime / count),
      avgUserPerceiveTime: Math.round(totals.userPerceiveTime / count),
      sampleCount: count,
    };
  }

  generateProgressiveReport(): string {
    const modals = ['drawing', 'indicators', 'interval', 'trade'];
    let report = '\n=== Progressive Modal Loading Report ===\n';
    report += 'Modal | Modal Open | Content Load | Total Time | User Perceive | Samples\n';
    report += '------|------------|--------------|------------|---------------|--------\n';

    modals.forEach(modal => {
      const avg = this.getAverageMetrics(modal);
      if (avg) {
        report += `${modal.padEnd(5)} | ${avg.avgModalOpenTime.toString().padEnd(10)} | ${avg.avgContentLoadTime.toString().padEnd(12)} | ${avg.avgTotalTime.toString().padEnd(10)} | ${avg.avgUserPerceiveTime.toString().padEnd(13)} | ${avg.sampleCount}\n`;
      }
    });

    const overall = this.getAverageMetrics();
    if (overall) {
      report += '------|------------|--------------|------------|---------------|--------\n';
      report += `Overall| ${overall.avgModalOpenTime.toString().padEnd(10)} | ${overall.avgContentLoadTime.toString().padEnd(12)} | ${overall.avgTotalTime.toString().padEnd(10)} | ${overall.avgUserPerceiveTime.toString().padEnd(13)} | ${overall.sampleCount}\n`;
    }

    return report;
  }

  getProgressiveInsights(): string[] {
    const insights: string[] = [];
    const overall = this.getAverageMetrics();
    
    if (!overall) return ['No data available for insights'];

    // Analyze modal opening performance
    if (overall.avgModalOpenTime <= 50) {
      insights.push('🚀 Excellent modal opening speed - users see response immediately');
    } else if (overall.avgModalOpenTime <= 150) {
      insights.push('✅ Good modal opening speed');
    } else {
      insights.push('⚠️ Modal opening could be faster');
    }

    // Analyze content loading performance
    if (overall.avgContentLoadTime <= 200) {
      insights.push('🚀 Excellent content loading - minimal skeleton time');
    } else if (overall.avgContentLoadTime <= 500) {
      insights.push('✅ Good content loading speed');
    } else {
      insights.push('⚠️ Content loading is taking too long');
    }

    // Analyze user experience
    if (overall.avgUserPerceiveTime <= 100) {
      insights.push('🎯 Outstanding user experience - instant feedback');
    } else {
      insights.push('💡 Consider optimizing initial modal rendering');
    }

    // Progressive loading effectiveness
    const progressiveEffectiveness = overall.avgUserPerceiveTime / overall.avgTotalTime;
    if (progressiveEffectiveness <= 0.3) {
      insights.push('🏆 Progressive loading is highly effective - users see 70%+ improvement');
    } else if (progressiveEffectiveness <= 0.5) {
      insights.push('✅ Progressive loading is working well');
    } else {
      insights.push('💡 Progressive loading could be more effective');
    }

    return insights;
  }

  getBenchmarkStatus(): string {
    const overall = this.getAverageMetrics();
    if (!overall) return 'No data available';

    const { avgModalOpenTime, avgContentLoadTime, avgUserPerceiveTime } = overall;
    
    // Performance thresholds for progressive modals (in ms)
    const EXCELLENT_MODAL_OPEN = 50;
    const GOOD_MODAL_OPEN = 150;
    const EXCELLENT_CONTENT_LOAD = 200;
    const GOOD_CONTENT_LOAD = 500;
    const EXCELLENT_USER_PERCEIVE = 100;
    const GOOD_USER_PERCEIVE = 200;

    let status = '';
    
    // Modal opening status
    if (avgModalOpenTime <= EXCELLENT_MODAL_OPEN) {
      status += '🚀 Excellent modal responsiveness';
    } else if (avgModalOpenTime <= GOOD_MODAL_OPEN) {
      status += '✅ Good modal responsiveness';
    } else {
      status += '⚠️ Modal responsiveness needs improvement';
    }

    status += ' | ';

    // Content loading status
    if (avgContentLoadTime <= EXCELLENT_CONTENT_LOAD) {
      status += '🚀 Excellent content loading';
    } else if (avgContentLoadTime <= GOOD_CONTENT_LOAD) {
      status += '✅ Good content loading';
    } else {
      status += '⚠️ Content loading needs improvement';
    }

    status += ' | ';

    // User experience status
    if (avgUserPerceiveTime <= EXCELLENT_USER_PERCEIVE) {
      status += '🚀 Excellent user experience';
    } else if (avgUserPerceiveTime <= GOOD_USER_PERCEIVE) {
      status += '✅ Good user experience';
    } else {
      status += '⚠️ User experience needs improvement';
    }

    return status;
  }

  clearMetrics() {
    this.metrics = [];
    this.modalStartTimes.clear();
    this.contentStartTimes.clear();
  }
}

// Global progressive modal tracker
export const progressiveModalTracker = new ProgressiveModalTracker();

/**
 * Hook for measuring progressive modal performance
 */
export const useProgressiveModalTracking = (modalName: string) => {
  const startModalOpen = () => {
    progressiveModalTracker.startModalOpen(modalName);
  };

  const modalVisible = () => {
    return progressiveModalTracker.modalVisible(modalName);
  };

  const contentLoaded = () => {
    return progressiveModalTracker.contentLoaded(modalName);
  };

  return {
    startModalOpen,
    modalVisible,
    contentLoaded,
  };
};

/**
 * Test runner for progressive modal performance
 */
export const runProgressiveModalTest = async (iterations: number = 5) => {
  console.log(`[Progressive Modal Test] Starting test with ${iterations} iterations`);
  
  progressiveModalTracker.clearMetrics();
  
  const modals = ['drawing', 'indicators', 'interval', 'trade'];
  
  for (let i = 0; i < iterations; i++) {
    console.log(`[Progressive Modal Test] Iteration ${i + 1}/${iterations}`);
    
    for (const modal of modals) {
      // Simulate progressive modal loading
      progressiveModalTracker.startModalOpen(modal);
      
      // Simulate modal opening (instant)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 20)); // 20-70ms
      const modalTime = progressiveModalTracker.modalVisible(modal);
      
      // Simulate content loading
      await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100)); // 100-400ms
      progressiveModalTracker.contentLoaded(modal);
      
      // Small delay between modals
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  const report = progressiveModalTracker.generateProgressiveReport();
  const status = progressiveModalTracker.getBenchmarkStatus();
  const insights = progressiveModalTracker.getProgressiveInsights();
  
  console.log(report);
  console.log(`\n${status}\n`);
  console.log('Progressive Loading Insights:');
  insights.forEach(insight => console.log(insight));
  
  return {
    report,
    status,
    insights,
    metrics: progressiveModalTracker.getMetrics(),
  };
};
