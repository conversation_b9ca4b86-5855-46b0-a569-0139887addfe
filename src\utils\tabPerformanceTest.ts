/**
 * Tab Performance Testing Utilities
 * Test suite for measuring tab switching performance improvements
 */

import { performanceTracker } from './performanceTest';

interface TabPerformanceMetrics {
  tabName: string;
  switchTime: number;
  renderTime: number;
  memoryUsage?: number;
  timestamp: number;
}

class TabPerformanceTracker {
  private metrics: TabPerformanceMetrics[] = [];
  private switchStartTime: number = 0;
  private currentTab: string = '';

  startTabSwitch(fromTab: string, toTab: string) {
    this.switchStartTime = Date.now();
    this.currentTab = toTab;
    
    if (__DEV__) {
      console.log(`[Tab Performance] Starting switch: ${fromTab} → ${toTab}`);
    }
  }

  endTabSwitch(tabName: string) {
    if (this.switchStartTime === 0) return;
    
    const switchTime = Date.now() - this.switchStartTime;
    const metric: TabPerformanceMetrics = {
      tabName,
      switchTime,
      renderTime: 0, // Will be updated by component
      timestamp: Date.now(),
    };

    this.metrics.push(metric);
    this.switchStartTime = 0;

    if (__DEV__) {
      console.log(`[Tab Performance] ${tabName} switch completed in ${switchTime}ms`);
    }

    return switchTime;
  }

  updateRenderTime(tabName: string, renderTime: number) {
    const metric = this.metrics.find(m => 
      m.tabName === tabName && 
      Math.abs(m.timestamp - Date.now()) < 5000 // Within 5 seconds
    );
    
    if (metric) {
      metric.renderTime = renderTime;
    }
  }

  getMetrics(tabName?: string): TabPerformanceMetrics[] {
    if (tabName) {
      return this.metrics.filter(m => m.tabName === tabName);
    }
    return [...this.metrics];
  }

  getAverageMetrics(tabName?: string) {
    const metrics = this.getMetrics(tabName);
    if (metrics.length === 0) return null;

    const avgSwitchTime = metrics.reduce((sum, m) => sum + m.switchTime, 0) / metrics.length;
    const avgRenderTime = metrics.reduce((sum, m) => sum + m.renderTime, 0) / metrics.length;

    return {
      tabName: tabName || 'all',
      avgSwitchTime: Math.round(avgSwitchTime),
      avgRenderTime: Math.round(avgRenderTime),
      sampleCount: metrics.length,
    };
  }

  generateReport(): string {
    const allTabs = [...new Set(this.metrics.map(m => m.tabName))];
    let report = '\n=== Tab Performance Report ===\n';
    report += 'Tab Name | Avg Switch Time | Avg Render Time | Samples\n';
    report += '---------|-----------------|-----------------|--------\n';

    allTabs.forEach(tabName => {
      const avg = this.getAverageMetrics(tabName);
      if (avg) {
        report += `${tabName.padEnd(8)} | ${avg.avgSwitchTime.toString().padEnd(15)} | ${avg.avgRenderTime.toString().padEnd(15)} | ${avg.sampleCount}\n`;
      }
    });

    const overall = this.getAverageMetrics();
    if (overall) {
      report += '---------|-----------------|-----------------|--------\n';
      report += `Overall  | ${overall.avgSwitchTime.toString().padEnd(15)} | ${overall.avgRenderTime.toString().padEnd(15)} | ${overall.sampleCount}\n`;
    }

    return report;
  }

  clearMetrics() {
    this.metrics = [];
    this.switchStartTime = 0;
  }

  // Performance benchmarks
  getBenchmarkStatus() {
    const overall = this.getAverageMetrics();
    if (!overall) return 'No data';

    const { avgSwitchTime, avgRenderTime } = overall;
    
    // Performance thresholds (in ms)
    const EXCELLENT_SWITCH = 200;
    const GOOD_SWITCH = 400;
    const EXCELLENT_RENDER = 100;
    const GOOD_RENDER = 300;

    let status = '';
    
    if (avgSwitchTime <= EXCELLENT_SWITCH) {
      status += '🚀 Excellent switch performance';
    } else if (avgSwitchTime <= GOOD_SWITCH) {
      status += '✅ Good switch performance';
    } else {
      status += '⚠️ Switch performance needs improvement';
    }

    status += ' | ';

    if (avgRenderTime <= EXCELLENT_RENDER) {
      status += '🚀 Excellent render performance';
    } else if (avgRenderTime <= GOOD_RENDER) {
      status += '✅ Good render performance';
    } else {
      status += '⚠️ Render performance needs improvement';
    }

    return status;
  }
}

// Global tab performance tracker
export const tabPerformanceTracker = new TabPerformanceTracker();

/**
 * Hook for measuring tab performance in components
 */
export const useTabPerformanceTracking = (tabName: string) => {
  const startRenderTracking = () => {
    performanceTracker.startTracking(`${tabName}-render`);
  };

  const endRenderTracking = () => {
    performanceTracker.endTracking(`${tabName}-render`, 'render');
    const metrics = performanceTracker.getMetrics(`${tabName}-render`);
    if (typeof metrics === 'object' && 'renderTime' in metrics) {
      tabPerformanceTracker.updateRenderTime(tabName, metrics.renderTime);
    }
  };

  return { startRenderTracking, endRenderTracking };
};

/**
 * Test runner for tab performance
 */
export const runTabPerformanceTest = async (tabs: string[], iterations: number = 5) => {
  console.log(`[Tab Performance Test] Starting test with ${iterations} iterations`);
  
  tabPerformanceTracker.clearMetrics();
  
  for (let i = 0; i < iterations; i++) {
    for (let j = 0; j < tabs.length; j++) {
      const fromTab = j === 0 ? tabs[tabs.length - 1] : tabs[j - 1];
      const toTab = tabs[j];
      
      tabPerformanceTracker.startTabSwitch(fromTab, toTab);
      
      // Simulate tab switch delay
      await new Promise(resolve => setTimeout(resolve, 100));
      
      tabPerformanceTracker.endTabSwitch(toTab);
      
      // Wait between switches
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
  
  const report = tabPerformanceTracker.generateReport();
  const status = tabPerformanceTracker.getBenchmarkStatus();
  
  console.log(report);
  console.log(`\n${status}\n`);
  
  return {
    report,
    status,
    metrics: tabPerformanceTracker.getMetrics(),
  };
};
