/**
 * Toolbar Performance Testing Utilities
 * Specialized testing for charts toolbar modal opening performance
 */

interface ToolbarInteractionMetrics {
  buttonName: string;
  clickTime: number;
  modalOpenTime: number;
  totalTime: number;
  timestamp: number;
}

class ToolbarPerformanceTracker {
  private metrics: ToolbarInteractionMetrics[] = [];
  private clickStartTimes: Map<string, number> = new Map();
  private modalStartTimes: Map<string, number> = new Map();

  startButtonClick(buttonName: string) {
    this.clickStartTimes.set(buttonName, Date.now());
    
    if (__DEV__) {
      console.log(`[Toolbar Performance] Button click started: ${buttonName}`);
    }
  }

  endButtonClick(buttonName: string) {
    const startTime = this.clickStartTimes.get(buttonName);
    if (!startTime) return 0;
    
    const clickTime = Date.now() - startTime;
    this.clickStartTimes.delete(buttonName);
    
    // Start modal timing
    this.modalStartTimes.set(buttonName, Date.now());
    
    if (__DEV__) {
      console.log(`[Toolbar Performance] Button click completed: ${buttonName} - ${clickTime}ms`);
    }
    
    return clickTime;
  }

  endModalOpen(buttonName: string) {
    const modalStartTime = this.modalStartTimes.get(buttonName);
    if (!modalStartTime) return 0;
    
    const modalOpenTime = Date.now() - modalStartTime;
    this.modalStartTimes.delete(buttonName);
    
    // Find the most recent click for this button
    const recentMetric = this.metrics
      .filter(m => m.buttonName === buttonName)
      .sort((a, b) => b.timestamp - a.timestamp)[0];
    
    if (recentMetric && Date.now() - recentMetric.timestamp < 5000) {
      // Update existing metric
      recentMetric.modalOpenTime = modalOpenTime;
      recentMetric.totalTime = recentMetric.clickTime + modalOpenTime;
    } else {
      // Create new metric (in case click timing was missed)
      const metric: ToolbarInteractionMetrics = {
        buttonName,
        clickTime: 0,
        modalOpenTime,
        totalTime: modalOpenTime,
        timestamp: Date.now(),
      };
      this.metrics.push(metric);
    }
    
    if (__DEV__) {
      console.log(`[Toolbar Performance] Modal opened: ${buttonName} - ${modalOpenTime}ms`);
    }
    
    return modalOpenTime;
  }

  recordInteraction(buttonName: string, clickTime: number, modalOpenTime: number) {
    const metric: ToolbarInteractionMetrics = {
      buttonName,
      clickTime,
      modalOpenTime,
      totalTime: clickTime + modalOpenTime,
      timestamp: Date.now(),
    };
    
    this.metrics.push(metric);
    
    if (__DEV__) {
      console.log(`[Toolbar Performance] Complete interaction: ${buttonName} - Click: ${clickTime}ms, Modal: ${modalOpenTime}ms, Total: ${metric.totalTime}ms`);
    }
  }

  getMetrics(buttonName?: string): ToolbarInteractionMetrics[] {
    if (buttonName) {
      return this.metrics.filter(m => m.buttonName === buttonName);
    }
    return [...this.metrics];
  }

  getAverageMetrics(buttonName?: string) {
    const metrics = this.getMetrics(buttonName);
    if (metrics.length === 0) return null;

    const totals = metrics.reduce((acc, m) => ({
      clickTime: acc.clickTime + m.clickTime,
      modalOpenTime: acc.modalOpenTime + m.modalOpenTime,
      totalTime: acc.totalTime + m.totalTime,
    }), { clickTime: 0, modalOpenTime: 0, totalTime: 0 });

    const count = metrics.length;
    return {
      buttonName: buttonName || 'all',
      avgClickTime: Math.round(totals.clickTime / count),
      avgModalOpenTime: Math.round(totals.modalOpenTime / count),
      avgTotalTime: Math.round(totals.totalTime / count),
      sampleCount: count,
    };
  }

  generateToolbarReport(): string {
    const buttons = ['interval', 'indicators', 'drawings', 'trade', 'more'];
    let report = '\n=== Toolbar Performance Report ===\n';
    report += 'Button | Click Time | Modal Open Time | Total Time | Samples\n';
    report += '-------|------------|-----------------|------------|--------\n';

    buttons.forEach(button => {
      const avg = this.getAverageMetrics(button);
      if (avg) {
        report += `${button.padEnd(6)} | ${avg.avgClickTime.toString().padEnd(10)} | ${avg.avgModalOpenTime.toString().padEnd(15)} | ${avg.avgTotalTime.toString().padEnd(10)} | ${avg.sampleCount}\n`;
      }
    });

    const overall = this.getAverageMetrics();
    if (overall) {
      report += '-------|------------|-----------------|------------|--------\n';
      report += `Overall| ${overall.avgClickTime.toString().padEnd(10)} | ${overall.avgModalOpenTime.toString().padEnd(15)} | ${overall.avgTotalTime.toString().padEnd(10)} | ${overall.sampleCount}\n`;
    }

    return report;
  }

  getBenchmarkStatus(): string {
    const overall = this.getAverageMetrics();
    if (!overall) return 'No data available';

    const { avgClickTime, avgModalOpenTime, avgTotalTime } = overall;
    
    // Performance thresholds for toolbar interactions (in ms)
    const EXCELLENT_CLICK = 16; // 1 frame at 60fps
    const GOOD_CLICK = 33; // 2 frames at 60fps
    const EXCELLENT_MODAL = 100;
    const GOOD_MODAL = 300;
    const EXCELLENT_TOTAL = 150;
    const GOOD_TOTAL = 400;

    let status = '';
    
    // Click responsiveness
    if (avgClickTime <= EXCELLENT_CLICK) {
      status += '🚀 Excellent click responsiveness';
    } else if (avgClickTime <= GOOD_CLICK) {
      status += '✅ Good click responsiveness';
    } else {
      status += '⚠️ Click responsiveness needs improvement';
    }

    status += ' | ';

    // Modal opening speed
    if (avgModalOpenTime <= EXCELLENT_MODAL) {
      status += '🚀 Excellent modal speed';
    } else if (avgModalOpenTime <= GOOD_MODAL) {
      status += '✅ Good modal speed';
    } else {
      status += '⚠️ Modal speed needs improvement';
    }

    status += ' | ';

    // Overall interaction time
    if (avgTotalTime <= EXCELLENT_TOTAL) {
      status += '🚀 Excellent overall performance';
    } else if (avgTotalTime <= GOOD_TOTAL) {
      status += '✅ Good overall performance';
    } else {
      status += '⚠️ Overall performance needs improvement';
    }

    return status;
  }

  clearMetrics() {
    this.metrics = [];
    this.clickStartTimes.clear();
    this.modalStartTimes.clear();
  }

  // Get performance insights
  getPerformanceInsights(): string[] {
    const insights: string[] = [];
    const overall = this.getAverageMetrics();
    
    if (!overall) return ['No data available for insights'];

    // Analyze click performance
    if (overall.avgClickTime > 33) {
      insights.push('⚠️ Button clicks are taking longer than 2 frames - consider optimizing event handlers');
    }

    // Analyze modal performance
    if (overall.avgModalOpenTime > 300) {
      insights.push('⚠️ Modals are taking too long to open - consider preloading or lazy loading optimization');
    }

    // Analyze individual buttons
    const buttons = ['interval', 'indicators', 'drawings', 'trade', 'more'];
    buttons.forEach(button => {
      const buttonMetrics = this.getAverageMetrics(button);
      if (buttonMetrics && buttonMetrics.avgTotalTime > 500) {
        insights.push(`⚠️ ${button} button is particularly slow - consider specific optimization`);
      }
    });

    if (insights.length === 0) {
      insights.push('✅ All toolbar interactions are performing well!');
    }

    return insights;
  }
}

// Global toolbar performance tracker
export const toolbarPerformanceTracker = new ToolbarPerformanceTracker();

/**
 * Hook for measuring toolbar performance in components
 */
export const useToolbarPerformanceTracking = () => {
  const measureButtonClick = (buttonName: string) => {
    const startTime = Date.now();
    toolbarPerformanceTracker.startButtonClick(buttonName);
    
    return {
      endClick: () => {
        const clickTime = toolbarPerformanceTracker.endButtonClick(buttonName);
        return clickTime;
      },
      endModal: () => {
        const modalTime = toolbarPerformanceTracker.endModalOpen(buttonName);
        return modalTime;
      }
    };
  };

  return { measureButtonClick };
};

/**
 * Test runner for toolbar performance
 */
export const runToolbarPerformanceTest = async (iterations: number = 10) => {
  console.log(`[Toolbar Performance Test] Starting test with ${iterations} iterations`);
  
  toolbarPerformanceTracker.clearMetrics();
  
  const buttons = ['interval', 'indicators', 'drawings', 'trade', 'more'];
  
  for (let i = 0; i < iterations; i++) {
    for (const button of buttons) {
      // Simulate button click
      const clickTime = Math.random() * 30 + 10; // 10-40ms
      const modalTime = Math.random() * 200 + 50; // 50-250ms
      
      toolbarPerformanceTracker.recordInteraction(button, clickTime, modalTime);
      
      // Small delay between interactions
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  const report = toolbarPerformanceTracker.generateToolbarReport();
  const status = toolbarPerformanceTracker.getBenchmarkStatus();
  const insights = toolbarPerformanceTracker.getPerformanceInsights();
  
  console.log(report);
  console.log(`\n${status}\n`);
  console.log('Performance Insights:');
  insights.forEach(insight => console.log(insight));
  
  return {
    report,
    status,
    insights,
    metrics: toolbarPerformanceTracker.getMetrics(),
  };
};
